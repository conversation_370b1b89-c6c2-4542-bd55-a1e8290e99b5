import { BetaAnalyticsDataClient } from '@google-analytics/data';
import config from 'src/config';
import {
  extractIdFromString,
  extractTitleFromString,
  isJSON,
} from 'src/helpers/utils';
import { logger } from 'src/log';

import { GoogleAuth } from 'google-auth-library';
import * as moment from "moment";

const LEAD_LABELS = {
  CONTACT: 'contact',
  SWEEP: 'sweep',
  IMAGE: 'image',
  ENTRY: 'entry',
};

export enum METRIC_AGGREGATIONS {
  TOTAL = 'TOTAL',
  MINIMUM = 'MINIMUM',
  MAXIMUM = 'MAXIMUM',
}

export enum MATCH_TYPE {
  BEGINS_WITH = 'BEGINS_WITH',
  EXACT = 'EXACT',
  ENDS_WITH = 'ENDS_WITH',
  CONTAINS = 'CONTAINS',
  REGEXP = 'REGEXP',
}

export enum AR_CATEGORIES {
  AR_PATH = 'arPath',
  AR_THREAD = 'arThread',
  AR_SCAN = 'arScan',
  AR_SWEEP = 'arSweep',
  AR_TOTAL_STEPS = 'totalSteps',
  AR_NAME = 'name',
  AR_END = 'end',
  AR_STEP = 'step',
  AR_START = 'start',
}

export enum V_COMMERCE_CATEGORIES {
  POPUP = 'vCommercePopup',
  LIKE = 'vCommerceLike',
  LIKE_MENU = 'vCommerceLikeMenu',
  ADD_TO_CART = 'vCommerceAddedToCart',
  PURCHASE = 'vCommercePurchase',
}

export default class GoogleClient {
  client: any;

  initilized: boolean = false;

  ga4Client: any;

  propertyId: string;

  dataStreamId: string;

  constructor() {
    this.client = new GoogleAuth({
      scopes: ['https://www.googleapis.com/auth/analytics.readonly'],
    });
    this.ga4Client = new BetaAnalyticsDataClient();
    this.propertyId = config.get('google.propertyId');
    this.dataStreamId = config.get('google.mobileStreamId');
  }

  async init() {
    this.client = await this.client.getClient();
    this.initilized = true;
  }

  private createDimensionFilter(
    matterId: string,
    slug: string,
    gameId?: number,
  ) {
    let pages = [`/tour/${slug}`, `/e-commerce-tour/${slug}`, `/asset/${slug}`];
    if (matterId) {
      pages.push(`/tour/${matterId}`);
    }

    if (
      gameId && //games should be on their own
      // add game path only in case if e-commerce is not applicable in current tour
      !pages.some((page) => page.startsWith('/e-commerce-tour'))
    ) {
      pages = [`/experience/${gameId}/${slug}`];
    }
    const filterExpressions = pages.map((pagePath) => {
      return {
        filter: {
          fieldName: 'pagePath',
          matchType: MATCH_TYPE.BEGINS_WITH,
          stringFilter: {
            value: pagePath,
          },
        },
      };
    });

    const filter = {
      orGroup: {
        expressions: filterExpressions,
      },
    };
    return filter;
  }

  async sources(
    slug: string,
    matterId: string,
    startDate: string,
    endDate: string,
    gameId?: number,
  ) {
    try {
      const dimensionFilter = this.createDimensionFilter(
        matterId,
        slug,
        gameId,
      );

      const ga4Res = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate,
            endDate,
          },
        ],
        metrics: [{ name: 'totalUsers' }, { name: 'screenPageViews' }],
        dimensions: [{ name: 'sessionSource' }],
        dimensionFilter: dimensionFilter,
      });

      const ans = [];

      if (ga4Res[0].rows) {
        ga4Res[0].rows.map((row: any) => {
          const totalUsers = Number.parseInt(row.metricValues[0].value);
          const screenPageViews = Number.parseInt(row.metricValues[1].value);
          let sessionSource = row.dimensionValues[0].value;

          if (
            sessionSource == '(direct)' ||
            sessionSource == 'my.treedis.com'
          ) {
            sessionSource = 'direct';
          }

          const existingValue = ans.find((item) => item.name === sessionSource);
          if (existingValue) {
            existingValue.users += totalUsers;
            existingValue.views += screenPageViews;
          } else {
            ans.push({
              name: sessionSource,
              users: totalUsers,
              views: screenPageViews,
              source: sessionSource,
            });
          }
        });
      }
      return ans;
    } catch (error) {
      return error;
    }
  }

  async totals(
    slug: string,
    matterId: string,
    startDate: string,
    endDate: string,
    gameId?: number,
  ) {
    try {
      const dimensionFilter = this.createDimensionFilter(
        matterId,
        slug,
        gameId,
      );

      const deviceGa4Res = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate,
            endDate,
          },
        ],
        metrics: [{ name: 'totalUsers' }],
        dimensions: [
          { name: 'deviceCategory' },
          { name: 'pagePath' },
          { name: 'operatingSystem' },
        ],
        dimensionFilter: dimensionFilter,
      });

      const ga4Res = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate,
            endDate,
          },
        ],
        metrics: [
          { name: 'totalUsers' },
          { name: 'userEngagementDuration' },
          { name: 'screenPageViews' },
          { name: 'activeUsers' },
        ],
        dimensions: [
          { name: 'date' },
          { name: 'deviceCategory' },
          { name: 'country' },
          { name: 'browser' },
          { name: 'pagePath' },
          { name: 'operatingSystem' },
        ],
        metricAggregations: [METRIC_AGGREGATIONS.TOTAL],
        dimensionFilter: dimensionFilter,
      });

      const ans: any = {
        users: 0,
        time: 0,
        views: 0,
        assetPageViews: 0,
        days: [],
        countries: [],
        browsers: [],
        usersByDevices: {},
      };

      if (ga4Res[0].rows) {
        ga4Res[0].rows.map((row: any) => {
          const users = Number.parseInt(row.metricValues[0].value);
          const userEngagementDuration = Number.parseInt(
            row.metricValues[1].value,
          );
          const views = Number.parseInt(row.metricValues[2].value);
          const activeUsers = Number.parseInt(row.metricValues[3].value);

          const avgSessionDuration =
            activeUsers > 0 ? userEngagementDuration / activeUsers : 0;

          ans.users += users;
          ans.time += avgSessionDuration;

          const date = moment(row.dimensionValues[0].value, 'YYYYMMDD').format(
            'DD/MM',
          );
          const device = row.dimensionValues[1].value;
          const countryName = row.dimensionValues[2].value;
          const browserName = row.dimensionValues[3].value;
          const firstParameter = row.dimensionValues[4].value.split('/')[1];
          const os = row.dimensionValues[5].value;

          if (firstParameter == 'asset') {
            ans.assetPageViews += views;
          } else {
            ans.views += views;
          }

          const dayOsDeviceIdentifier = `${date}-${os}-${device}`;

          const day = ans.days.find(
            (item: { id: string }) => item.id === dayOsDeviceIdentifier,
          );

          if (day) {
            day.users += users;
            day.time += avgSessionDuration;
            day.views += views;
          } else {
            ans.days.push({
              id: dayOsDeviceIdentifier,
              date,
              users,
              time: avgSessionDuration,
              views,
              device,
              os,
            });
          }

          const country = ans.countries.find(
            (item: { countryName: string }) => item.countryName === countryName,
          );
          if (country) {
            country.sessions += views;
          } else {
            ans.countries.push({ countryName, sessions: views });
          }

          const browser = ans.browsers.find(
            (item: { browserName: string }) => item.browserName === browserName,
          );
          if (browser) {
            browser.sessions += views;
          } else {
            ans.browsers.push({ browserName, sessions: views });
          }
        });
      }

      if (ga4Res[0].totals?.[0].metricValues?.length) {
        const totalMetrics = ga4Res[0].totals[0].metricValues;

        const totalTime = Number.parseInt(totalMetrics[1].value);
        const totalViews = Number.parseInt(totalMetrics[2].value);
        const activeUsers = Number.parseInt(totalMetrics[3].value);

        ans.users = activeUsers;
        ans.views = totalViews;
        ans.time = (totalTime / activeUsers).toFixed(2);
      } else {
        ans.time = Number.parseFloat((ans.time / ans.days.length).toFixed(2));
      }

      if (deviceGa4Res[0].rows) {
        deviceGa4Res[0].rows.map(() => {
          if (deviceGa4Res[0].rows) {
            const deviceCategories = {
              ios: 0,
              android: 0,
              desktop: 0,
              tablet: 0,
            };

            deviceGa4Res[0].rows.forEach((row) => {
              const deviceCategory = row.dimensionValues[0].value;
              const operatingSystem = row.dimensionValues[2].value;
              const users = Number.parseInt(row.metricValues[0].value);

              switch (deviceCategory) {
                case 'mobile': {
                  if (operatingSystem === 'iOS') {
                    deviceCategories.ios += users;
                  } else if (operatingSystem === 'Android') {
                    deviceCategories.android += users;
                  }

                  break;
                }
                case 'desktop': {
                  deviceCategories.desktop += users;

                  break;
                }
                case 'tablet': {
                  deviceCategories.tablet += users;

                  break;
                }
                // No default
              }
            });
            ans.usersByDevices = deviceCategories;
          }
        });
      }

      return ans;
    } catch (error) {
      return error;
    }
  }

  async avarageExperienceTime(data) {
    const { matterId, slug, gameId, startDate, endDate } = data;
    try {
      const dimensionFilter = this.createDimensionFilter(
        matterId,
        slug,
        gameId,
      );

      const [ga4Res] = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate,
            endDate,
          },
        ],
        metrics: [{ name: 'userEngagementDuration' }, { name: 'activeUsers' }],
        metricAggregations: [METRIC_AGGREGATIONS.TOTAL],
        dimensionFilter: dimensionFilter,
      });

      if (ga4Res.totals?.[0].metricValues?.length) {
        const totalMetrics = ga4Res.totals[0].metricValues;

        const totalTime = Number.parseInt(totalMetrics[0].value);
        const activeUsers = Number.parseInt(totalMetrics[1].value);

        return (totalTime / activeUsers).toFixed(2);
      } else {
        return null;
      }
    } catch (error) {
      logger.error(`avarageExperienceTime error: ${error}`);
      return null;
    }
  }

  async totalsByHours(
    slug: string,
    matterId: string,
    startDate: string,
    endDate: string,
    gameId?: number,
  ) {
    try {
      const dimensionFilter = this.createDimensionFilter(
        matterId,
        slug,
        gameId,
      );

      const ga4Res = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate,
            endDate,
          },
        ],
        metrics: [
          { name: 'totalUsers' },
          { name: 'screenPageViews' },
          { name: 'userEngagementDuration' },
          { name: 'activeUsers' },
        ],
        dimensions: [{ name: 'date' }, { name: 'hour' }],
        dimensionFilter: dimensionFilter,
      });

      const ans = {
        users: 0,
        views: 0,
        time: 0,
        days: [],
      };

      if (ga4Res[0].rows) {
        ga4Res[0].rows.map((row: any) => {
          const totalUsers = Number.parseInt(row.metricValues[0].value);
          const screenPageViews = Number.parseInt(row.metricValues[1].value);
          const userEngagementDuration = Number.parseInt(
            row.metricValues[2].value,
          );
          const activeUsers = Number.parseInt(row.metricValues[3].value);

          const avgSessionDuration = activeUsers
            ? userEngagementDuration / activeUsers
            : 0;

          const date = moment(
            row.dimensionValues[0].value + row.dimensionValues[1].value,
            'YYYYMMDDHH',
          ).format('DD/MM/HH:mm');

          const existingDay = ans.days.find((item) => item.date === date);
          if (existingDay) {
            existingDay.users += totalUsers;
            existingDay.views += screenPageViews;
            existingDay.time += avgSessionDuration;
          } else {
            ans.days.push({
              date,
              users: totalUsers,
              views: screenPageViews,
              time: avgSessionDuration,
            });
          }

          ans.users += totalUsers;
          ans.views += screenPageViews;
          ans.time += avgSessionDuration;
        });
      }

      return ans;
    } catch (error) {
      return error;
    }
  }

  async events(
    slug: string,
    matterId: string,
    startDate: string,
    endDate: string,
    gameId?: number,
  ) {
      const dimensionFilter = this.createDimensionFilter(
        matterId,
        slug,
        gameId,
      );

      const ga4Res1 = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate: startDate,
            endDate: endDate,
          },
        ],
        dimensions: [
          { name: 'customEvent:event_category' },
          { name: 'eventName' },
          { name: 'customEvent:event_label' },
        ],
        metrics: [{ name: 'eventCount' }],
        dimensionFilter: dimensionFilter,
      });
      const ga4Res2 = await this.ga4Client.runReport({
        property: `properties/${process.env.GA4_PROPERTY_ID}`,
        dateRanges: [
          {
            startDate: startDate,
            endDate: endDate,
          },
        ],
        dimensions: [
          { name: 'customEvent:event_category' },
          { name: 'eventName' },
          { name: 'customEvent:event_label' },
          { name: 'customEvent:event_id' },
          { name: 'customEvent:event_type' },
          { name: 'customEvent:event_quantity' },
        ],
        metrics: [{ name: 'eventCount' }],
        dimensionFilter: dimensionFilter,
      });

      const ans = {
        totalIntroButton: 0,
        totalTags: 0,
        totalPlaces: 0,
        totalMaps: 0,
        totalProducedBy: 0,
        totalGetInTouch: 0,
        totalBookNow: 0,
        totalPolygons: 0,
        totalTicketingLoad: 0,
        totalTicketingPurchase: 0,
        tags: [],
        polygons: [],
        places: [],
        contact: [],
        gameEvents: [],
        arAnalytic: {
          pathActivity: [],
          total: {
            totalSum: 0,
            midTime: 0,
            scanCount: 0,
            newThreads: 0,
          },
          paths: [],
          heatMap: {},
        },
        highlightReels: [],
        sweepGroups: [],
        vCommerce: [],
        leadEvents: {},
        sweeps: {},
      };

      ans.leadEvents = {
        contact: {
          open: 0,
          submit: 0,
        },
        sweep: {
          open: 0,
          submit: 0,
        },
        entry: {
          open: 0,
          submit: 0,
        },
        image: {
          open: 0,
          submit: 0,
        },
      };

      const combinedRows = [];
      const seenEvents = new Set();

      for (const rowsArray of [ga4Res1[0].rows, ga4Res2[0].rows]) {
        for (const row of rowsArray) {
          const category = row.dimensionValues[0].value;
          const action = row.dimensionValues[1].value;
          const label = row.dimensionValues[2].value;

          const eventId = `${category}_${action}_${label}`;

          if (!seenEvents.has(eventId)) {
            seenEvents.add(eventId);
            combinedRows.push(row);
          }
        }
      }

      if (combinedRows.length > 0) {
        for (const row of combinedRows) {
          const category = row.dimensionValues[0].value;
          const action = row.dimensionValues[1].value;
          const label = row.dimensionValues[2].value;
          const type =
            row.dimensionValues?.[4]?.value !== '(not set)'
              ? row.dimensionValues?.[4]?.value
              : null;
          const quantity =
            row.dimensionValues?.[5]?.value !== '(not set)'
              ? row.dimensionValues?.[5]?.value
              : 0;
          const id =
            row.dimensionValues?.[3]?.value !== '(not set)'
              ? row.dimensionValues?.[3]?.value
              : null;

          const value = Number.parseInt(row.metricValues[0].value);

          if (action === 'ar-path') {
            const eventUUID = row.dimensionValues?.[3]?.value;
            logger.info(
              `AR-PATH-ACTION WITH CATEGORY: ${category}, Full event: ${JSON.stringify(
                { category, action, label, eventUUID, value },
              )}`,
            );
            const foundPathActivityData = ans.arAnalytic.pathActivity.find(
              (item: any) => item.uuid === eventUUID,
            );
            const foundPathsData = ans.arAnalytic.paths.find(
              (item: any) => item.uuid === eventUUID,
            );
            if (category === 'total-steps') {
              if (!foundPathActivityData) {
                ans.arAnalytic.pathActivity.push({
                  date: 0,
                  name: '',
                  steps: 0,
                  uuid: eventUUID,
                  totalSteps: value,
                });
              } else {
                foundPathActivityData.totalSteps = value;
              }
              if (!foundPathsData) {
                ans.arAnalytic.paths.push({
                  name: '',
                  timesUsed: 0,
                  uuid: eventUUID,
                  totalSteps: value,
                });
              } else {
                foundPathsData.totalSteps = value;
              }
            }
            if (category === 'step') {
              if (!foundPathActivityData) {
                ans.arAnalytic.pathActivity.push({
                  date: 0,
                  name: '',
                  steps: value,
                  uuid: eventUUID,
                  totalSteps: 0,
                });
              } else {
                foundPathActivityData.steps = value;
              }
            }
            if (category === 'start') {
              if (!foundPathActivityData) {
                ans.arAnalytic.pathActivity.push({
                  date: value,
                  name: '',
                  steps: 0,
                  uuid: eventUUID,
                  totalSteps: 0,
                });
              } else {
                foundPathActivityData.date = value;
              }
            }
            if (category === 'end') {
              if (!foundPathActivityData) {
                ans.arAnalytic.pathActivity.push({
                  date: value,
                  name: '',
                  steps: 0,
                  uuid: eventUUID,
                  totalSteps: 0,
                });
              } else {
                foundPathActivityData.date = value;
              }
            }
          }

          if (category != 'Mattertag' && category != 'Place') {
            let name;
            if (category == 'Book Now' && action == 'click') name = 'Book Now';
            if (category == 'Contact' && action == 'click' && label == 'phone')
              name = 'Phone';
            if (category == 'Contact' && action == 'click' && label == 'email')
              name = 'E-Mail';
            if (category == 'Contact' && action == 'click' && label == 'map')
              name = 'Map';
            if (category == 'Contact' && action == 'click' && label == 'open')
              name = 'Open';
            if (category == 'Contact' && action == 'click' && label == 'submit')
              name = 'Submit';

            if (name) {
              const index = ans.contact.findIndex(
                (object) => object.name == name,
              );
              if (index == -1) {
                ans.contact.push({ name: name, clicks: value });
              } else {
                ans.contact[index].clicks += value;
              }
            }
          }

          if (category == 'Contact' && action == 'click' && label == 'map') {
            ans.totalMaps += value;
          }
          if (category == 'Polygon' && action == 'click') {
            const polygonClickEventObject = isJSON(label)
              ? JSON.parse(label)
              : { title: extractTitleFromString(label) || label, sweep_id: id };

            ans.polygons.push({
              name: polygonClickEventObject?.title,
              clicks: value,
              sweep_id: polygonClickEventObject?.sweep_id,
            });
            ans.totalPolygons += value;
          }
          if (category == 'Ticketing' && action == 'click') {
            if (action === 'purchase') {
              ans.totalTicketingPurchase += value;
            } else if (action === 'welcome_screen') {
              ans.totalTicketingLoad += value;
            }
          }
          if (category == 'Mattertag' && action == 'click') {
            const tagClickObject = isJSON(label)
              ? JSON.parse(label)
              : { title: extractTitleFromString(label) || label, type };

            ans.tags.push({
              name: tagClickObject?.title,
              clicks: value,
              type: tagClickObject?.type,
            });
            ans.totalTags += value;
          }
          if (category == 'BookNow' && action == 'click') {
            ans.totalBookNow += value;
          }
          if (category == 'Contact' && action == 'click') {
            ans.totalGetInTouch += value;
          }
          if (category == 'ProducedBy' && action == 'click') {
            ans.totalProducedBy += value;
          }
          if (category == 'Highlight' && action == 'click') {
            const hrClicksObject = isJSON(label)
              ? JSON.parse(label)
              : { title: extractTitleFromString(label) || label, id: id };

            ans.highlightReels.push({
              id: hrClicksObject?.id,
              name: hrClicksObject?.title,
              clicks: value,
            });
          }
          if (category == V_COMMERCE_CATEGORIES.POPUP) {
            const vCommerceObject = isJSON(label)
              ? JSON.parse(label)
              : { id: extractIdFromString(label) || id };

            const foundData = ans.vCommerce.find(
              (item) => item.id == vCommerceObject?.id,
            );

            if (foundData) {
              foundData.clicks += value;
            } else if (vCommerceObject)
              ans.vCommerce.push({
                id: vCommerceObject?.id,
                clicks: value,
                likes: 0,
                likesMenu: 0,
                addedToCart: 0,
                purchases: 0,
              });
          }
          if (category == V_COMMERCE_CATEGORIES.LIKE) {
            const vCommerceObject = isJSON(label)
              ? JSON.parse(label)
              : { id: extractIdFromString(label) || id };

            const foundData = ans.vCommerce.find(
              (item) => item.id == vCommerceObject?.id,
            );

            if (foundData) {
              foundData.likes += value;
            } else if (vCommerceObject)
              ans.vCommerce.push({
                id: vCommerceObject?.id,
                clicks: 0,
                likes: value,
                likesMenu: 0,
                addedToCart: 0,
                purchases: 0,
              });
          }

          if (category == V_COMMERCE_CATEGORIES.LIKE_MENU) {
            const vCommerceObject = isJSON(label)
              ? JSON.parse(label)
              : { id: extractIdFromString(label) || id };

            const foundData = ans.vCommerce.find(
              (item) => item.id == vCommerceObject?.id,
            );
            if (foundData) {
              foundData.likesMenu += value;
            } else if (vCommerceObject)
              ans.vCommerce.push({
                id: vCommerceObject?.id,
                clicks: 0,
                likes: 0,
                likesMenu: value,
                addedToCart: 0,
                purchases: 0,
              });
          }
          if (category == V_COMMERCE_CATEGORIES.ADD_TO_CART) {
            const vCommerceObject = isJSON(label)
              ? JSON.parse(label)
              : { id: extractIdFromString(label) || id };

            const foundData = ans.vCommerce.find(
              (item) => item.id == vCommerceObject?.id,
            );

            if (foundData) {
              foundData.addedToCart += +quantity;
            } else if (vCommerceObject)
              ans.vCommerce.push({
                id: vCommerceObject?.id,
                clicks: 0,
                likes: 0,
                likesMenu: 0,
                addedToCart: +quantity,
                purchases: 0,
              });
          }
          if (category == V_COMMERCE_CATEGORIES.PURCHASE) {
            const vCommerceObject = isJSON(label)
              ? JSON.parse(label)
              : { id: extractIdFromString(label) || id };

            const foundData = ans.vCommerce.find(
              (item) => item.id == vCommerceObject?.id,
            );

            if (foundData) {
              foundData.purchases += +quantity;
            } else if (vCommerceObject)
              ans.vCommerce.push({
                id: vCommerceObject?.id,
                clicks: 0,
                likes: 0,
                likesMenu: 0,
                addedToCart: 0,
                purchases: +quantity,
              });
          }
          if (category == 'SweepGroup' && action == 'move') {
            ans.sweepGroups.push({
              name: label,
              visits: value,
            });
          }

          if (
            category == 'Place' &&
            action == 'click' &&
            label != '(not set)'
          ) {
            ans.places.push({
              name: isJSON(label)
                ? JSON.parse(label).title
                : extractTitleFromString(label) || label,
              clicks: value,
              type: isJSON(label) ? JSON.parse(label).type : type || 'No Data',
            });
            ans.totalPlaces += value;
          }
          if (category == 'Sweep' && action == 'move') {
            ans.sweeps[label] = value;
          }
          if (
            category == 'IntroScreen' &&
            action == 'click' &&
            label == 'introButton'
          ) {
            ans.totalIntroButton += value;
          }
          if (
            category == 'Lead' &&
            (label == LEAD_LABELS.CONTACT ||
              label == LEAD_LABELS.SWEEP ||
              label == LEAD_LABELS.IMAGE ||
              label == LEAD_LABELS.ENTRY) &&
            (action == 'open' || action == 'submit')
          ) {
            ans.leadEvents[label][action] += value;
          }
        }
      }

      return ans;
  }

  private createARDimensionFilter(dataStreamId: string, slug: string) {
    return {
      andGroup: {
        expressions: [
          {
            filter: {
              fieldName: 'streamId',
              stringFilter: {
                matchType: MATCH_TYPE.EXACT,
                value: dataStreamId,
              },
            },
          },
          {
            filter: {
              fieldName: 'customEvent:event_slug',
              stringFilter: {
                matchType: MATCH_TYPE.EXACT,
                value: slug,
              },
            },
          },
        ],
      },
    };
  }

  async arAnalytic(slug: string, startDate: string, endDate: string) {
    const dimensionFilter = this.createARDimensionFilter(
      this.dataStreamId,
      slug,
    );

    const ans = this.initializeAnalyticsResult();

    const [ga4Res] = await this.ga4Client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [
        { name: 'customEvent:event_category' },
        { name: 'eventName' },
        { name: 'customEvent:event_label' },
        { name: 'customEvent:event_id' },
        { name: 'date' },
      ],
      metrics: [
        { name: 'eventCount' },
        { name: 'userEngagementDuration' },
        { name: 'averageSessionDuration' },
      ],
      metricAggregations: [METRIC_AGGREGATIONS.TOTAL],
      dimensionFilter: dimensionFilter,
    });

    if (ga4Res.rows.length > 0) {
      for (const row of ga4Res.rows) {
        this.processAnalyticsRow(row, ans);
      }

      if (ga4Res.totals?.[0].metricValues?.length) {
        this.processAnalyticsTotals(ga4Res.totals[0].metricValues, ans);
      }
    }

    return ans;
  }

  private initializeAnalyticsResult() {
    return {
      pathActivity: [],
      total: {
        totalTime: 0,
        midTime: 0,
        scanCount: 0,
        newThreads: 0,
      },
      paths: [],
      hotspots: {},
    };
  }

  private processAnalyticsRow(row: any, ans: any) {
    const category = row.dimensionValues[0].value;
    const eventName = row.dimensionValues[1].value;
    const label = row.dimensionValues[2].value;
    const eventId = row.dimensionValues[3].value;
    const date = moment(row.dimensionValues[4].value, 'YYYYMMDD').format(
      'DD/MM',
    );
    const eventCount = Number.parseInt(row.metricValues[0].value);

    if (category === AR_CATEGORIES.AR_SCAN) {
      ans.total.scanCount += eventCount;
    }

    if (category === AR_CATEGORIES.AR_THREAD) {
      ans.total.newThreads += eventCount;
    }

    if (eventName === AR_CATEGORIES.AR_PATH) {
      this.processPathActivity(category, label, eventId, date, eventCount, ans);
    }

    if (category === AR_CATEGORIES.AR_SWEEP && eventName === 'move') {
      ans.hotspots[label] = (ans.hotspots[label] || 0) + eventCount;
    }
  }

  private processPathActivity(
    category: string,
    label: string,
    eventId: string,
    date: string,
    eventCount: number,
    ans: any,
  ) {
    let foundPathActivityData = ans.pathActivity.find(
      (item: any) => item.date === date,
    );
    if (!foundPathActivityData) {
      foundPathActivityData = {
        date,
        steps: 0,
        uuid: eventId,
        name: [],
        timeStart: 0,
        timeEnd: 0,
        totalSum: 0,
      };
      ans.pathActivity.push(foundPathActivityData);
    }

    let foundPathsData = ans.paths.find((item: any) => item.uuid === eventId);
    if (!foundPathsData) {
      foundPathsData = { uuid: eventId, totalSteps: 0, timesUsed: 0 };
      ans.paths.push(foundPathsData);
    }

    switch (category) {
      case AR_CATEGORIES.AR_NAME: {
        foundPathActivityData.name.push(label);
        foundPathsData.name = label;
        break;
      }
      case AR_CATEGORIES.AR_STEP: {
        foundPathActivityData.steps += eventCount;
        break;
      }
      case AR_CATEGORIES.AR_TOTAL_STEPS: {
        foundPathsData.timesUsed += 1;
        foundPathsData.totalSteps += eventCount;
        break;
      }
      case AR_CATEGORIES.AR_START: {
        foundPathActivityData.timeStart = eventCount;
        break;
      }
      case AR_CATEGORIES.AR_END: {
        foundPathActivityData.timeEnd = eventCount;
        foundPathActivityData.totalSum +=
          foundPathActivityData.timeEnd - foundPathActivityData.timeStart;
        break;
      }
    }
  }

  private processAnalyticsTotals(totalMetrics: any, ans: any) {
    ans.total.totalTime = Number.parseInt(totalMetrics[1].value);
    ans.total.midTime = Number.parseInt(totalMetrics[2].value);
  }
}

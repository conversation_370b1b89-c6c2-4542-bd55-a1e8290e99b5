// import { CreateAnalyticType } from '@treedis/db-access';
import config from "src/config";
import * as correlator from "src/helpers/correlationId";
import { logger } from "src/log";

const Queue = require("bee-queue");
const analyticQueue = new Queue("analytic", {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: false,
});
export default class AnalyticQueue {
  // public async createAnalyticRecord(data: CreateAnalyticType) {
  //   if (!config.get("mongo.enabled")) return;
  //   const job = analyticQueue.createJob({
  //     action: "createAnalytic",
  //     data,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(2)
  //     .save()
  //     .then((job: any) => {
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `bot create analytic | jobId: ${job.id} | ${
  //           error.message
  //         } | data: ${JSON.stringify(data)}`
  //       );
  //       throw error;
  //     });
  // }
  // public async saveGameAnalytic(data: CreateAnalyticType) {
  //   if (!config.get("mongo.enabled")) return;
  //   const job = analyticQueue.createJob({
  //     action: "saveGameAnalytic",
  //     data,
  //     correlationId: correlator.getId(),
  //   });
  //   return job
  //     .timeout(600_000)
  //     .retries(2)
  //     .save()
  //     .then((job: any) => {
  //       return new Promise((resolve, reject) => {
  //         job.on("succeeded", (result: any) => {
  //           return resolve(result);
  //         });
  //         job.on("failed", (error: Error) => {
  //           return reject(error);
  //         });
  //         job.on("error", (error: Error) => {
  //           return reject(error);
  //         });
  //       });
  //     })
  //     .catch((error: Error) => {
  //       logger.error(
  //         `bot create analytic | jobId: ${job.id} | ${
  //           error.message
  //         } | data: ${JSON.stringify(data)}`
  //       );
  //       throw error;
  //     });
  // }
}

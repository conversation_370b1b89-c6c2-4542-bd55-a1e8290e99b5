export default class AwsService {
  acm: any;

  cloudfront: any;

  constructor(acm, cloudfront) {
    this.acm = acm;
    this.cloudfront = cloudfront;
  }

  public async listCertificates() {
    return new Promise((resolve: any, reject: any) => {
      this.acm.listCertificates(
        { MaxItems: '1000' },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data.CertificateSummaryList);
        },
      );
    });
  }

  public async describeCertificateArn(arn: string) {
    return new Promise((resolve: any, reject: any) => {
      this.acm.describeCertificate(
        { CertificateArn: arn },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data.Certificate);
        },
      );
    });
  }

  public async getDistribution(id: string) {
    return new Promise((resolve: any, reject: any) => {
      this.cloudfront.getDistribution({ Id: id }, (error: any, data: any) => {
        if (error) return reject(error);
        return resolve(data);
      });
    });
  }

  public async getDistributionConfig(id: string) {
    return new Promise((resolve: any, reject: any) => {
      this.cloudfront.getDistributionConfig(
        { Id: id },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data);
        },
      );
    });
  }

  public async disableDistribution(id: string, distribution: any) {
    return new Promise((resolve: any, reject: any) => {
      this.cloudfront.updateDistribution(
        {
          DistributionConfig: {
            ...distribution.DistributionConfig,
            Enabled: false,
          },
          IfMatch: distribution.ETag,
          Id: id,
        },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data);
        },
      );
    });
  }

  public async deleteCertificateArn(arn: string) {
    return new Promise((resolve: any, reject: any) => {
      this.acm.deleteCertificate(
        { CertificateArn: arn },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data);
        },
      );
    });
  }

  public async deleteDistribution(id: string, eTag: string) {
    return new Promise((resolve: any, reject: any) => {
      this.cloudfront.deleteDistribution(
        {
          Id: id,
          IfMatch: eTag,
        },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data);
        },
      );
    });
  }

  public async waitUpdateDistribution(id: string) {
    return new Promise((resolve: any, reject: any) => {
      this.cloudfront.waitFor(
        'distributionDeployed',
        {
          Id: id,
        },
        (error: any, data: any) => {
          if (error) return reject(error);
          return resolve(data);
        },
      );
    });
  }
}

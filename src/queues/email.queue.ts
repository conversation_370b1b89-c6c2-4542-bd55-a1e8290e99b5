import * as correlator from 'src/helpers/correlationId';
import { logger } from 'src/log';

const Queue = require('bee-queue');

const emailQueue = new Queue('email', {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

export default class EmailQueue {
  constructor() {}

  public async sendEmail(
    emailType: string,
    sender: any = process.env.TREEDIS_EMAIL,
    to: string,
    parameters: any,
    throwError: boolean = false,
  ) {
    if (!to) return;
    let from = sender;
    //whitelabel logic
    if (typeof from !== 'string') {
      from = sender.whitelabelEmail;
      if (sender.admin && sender.admin.id !== 1)
        from = sender.admin.whitelabelEmail;
      if (!from) from = process.env.TREEDIS_EMAIL;
    }
    const job = emailQueue.createJob({
      action: emailType,
      from: from,
      to: to,
      params: parameters,
      correlationId: correlator.getId(),
    });
    return job
      .timeout(3000)
      .retries(0)
      .save()
      .then((job: any) => {
        logger.info(`email | jobId: ${job.id} | emailType: ${emailType}`);
        return new Promise((resolve, reject) => {
          job.on('succeeded', (result: any) => {
            return resolve(result);
          });
          job.on('failed', (error: Error) => {
            return reject(error);
          });
          job.on('error', (error: Error) => {
            return reject(error);
          });
        });
      })
      .catch((error: Error) => {
        if (throwError) throw error;
        else logger.error(`email | jobId: ${job.id} | ${error.message}`);
      });
  }
}

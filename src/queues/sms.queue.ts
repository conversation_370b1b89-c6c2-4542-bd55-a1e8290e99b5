import * as correlator from 'src/helpers/correlationId';

const Queue = require('bee-queue');

const logger = require('../log');
const smsQueue = new Queue('sms', {
  redis: { url: process.env.REDISCLOUD_URL },
  isWorker: false,
  removeOnSuccess: true,
  removeOnFailure: true,
});

export default class SmsQueue {
  public async sendSms(
    smsType: string,
    sender: string,
    to: string,
    parameters: any,
    throwError: boolean = false,
  ) {
    if (!to) return;
    const job = smsQueue.createJob({
      action: smsType,
      from: sender,
      to: `+${to}`,
      params: parameters,
      correlationId: correlator.getId(),
    });
    return job
      .timeout(3000)
      .retries(0)
      .save()
      .then((job: any) => {
        logger.info(`sms | jobId: ${job.id} | smsType: ${smsType}`);
        return new Promise((resolve, reject) => {
          job.on('succeeded', (result: any) => {
            return resolve(result);
          });
          job.on('failed', (error: Error) => {
            return reject(error);
          });
          job.on('error', (error: Error) => {
            return reject(error);
          });
        });
      })
      .catch((error: Error) => {
        if (throwError) throw error;
        else logger.error(`sms | jobId: ${job.id} | ${error.message}`);
      });
  }
}

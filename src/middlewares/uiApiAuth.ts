import { NextFunction, Request, Response } from 'express';

import Config from '../config';

export default (request: Request | any, res: Response, next: NextFunction) => {
  const auth = {
    login: Config.get('api.uiAuth.login'),
    password: Config.get('api.uiAuth.password'),
  };
  const b64auth = (request.headers.authorization || '').split(' ')[1] || '';
  const [login, password] = Buffer.from(b64auth, 'base64')
    .toString()
    .split(':');

  if (login && password && login === auth.login && password === auth.password) {
    return next();
  }

  res.set('WWW-Authenticate', 'Basic realm="401"');
  res.status(401).json('Authentication required.');
};

import * as correlator from '../helpers/correlationId';

function correlationIdMiddleware(request, res, next) {
  correlator.bindEmitter(request);
  correlator.bindEmitter(res);
  correlator.bindEmitter(request.socket);

  correlator.withId(() => {
    const currentCorrelationId = correlator.getId();
    res.set(`x-correlation-id`, currentCorrelationId);
    next();
  }, request.get(`x-correlation-id`));
}

export { correlationIdMiddleware };

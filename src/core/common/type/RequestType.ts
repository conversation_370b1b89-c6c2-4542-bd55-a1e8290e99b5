import { HttpUserPayload } from '../type';

export type RequestFiles = {
  [fieldName: string]: Express.Multer.File[];
};
export type RequestFile = Express.Multer.File | undefined;

export type RequestType<T> = Omit<T, 'account'> & {
  account: HttpUserPayload;
  query: {
    [key: string]: any;
  };
  params: {
    [key: string]: any;
  };
  files: RequestFiles;
  file: RequestFile;
  isNewAdmin: boolean; // TODO need to be changed after old crm is gone
};

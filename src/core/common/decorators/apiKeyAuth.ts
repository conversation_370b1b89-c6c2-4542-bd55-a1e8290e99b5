import UserController from '../../../controllers/user';
import { CoreApiResponse } from '../api';
import { Code } from '../code/Code';

export default function ApiKeyAuth() {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const original = descriptor.value;

    descriptor.value = async function (request, res, next) {
      try {
        request.account = await UserController.getByApiKey(
          request.get('X-API-Key'),
        );
        request.account.selfId = request.account.id;
        return await Reflect.apply(original, this, [request, res, next]);
      } catch {
        return res
          .status(Code.ACCESS_DENIED_ERROR.code)
          .json(
            CoreApiResponse.error(
              Code.ACCESS_DENIED_ERROR.code,
              Code.ACCESS_DENIED_ERROR.message,
            ),
          );
      }
    };

    return descriptor;
  };
}

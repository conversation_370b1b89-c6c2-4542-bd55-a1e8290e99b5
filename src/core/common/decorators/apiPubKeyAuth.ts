import { CoreApiResponse } from '../api';
import { Code } from '../code/Code';

export default function ApiPubKeyAuth() {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const original = descriptor.value;

    descriptor.value = async function (request, res, next) {
      try {
        if (request.get('X-API-Key') !== process.env.PUB_API_KEY)
          throw new Error('Wrong API key');
        return await Reflect.apply(original, this, [request, res, next]);
      } catch {
        return res
          .status(Code.ACCESS_DENIED_ERROR.code)
          .json(
            CoreApiResponse.error(
              Code.ACCESS_DENIED_ERROR.code,
              Code.ACCESS_DENIED_ERROR.message,
            ),
          );
      }
    };

    return descriptor;
  };
}

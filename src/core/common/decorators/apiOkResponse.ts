import { CoreApiResponse } from '../api';

export default function apiOkResponse({ type, isArray = false }) {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const original = descriptor.value;
    descriptor.value = async function (...arguments_: any[]) {
      const [request, res, next] = arguments_;
      const response = await Reflect.apply(original, this, [
        request,
        res,
        next,
      ]);
      if (isArray) {
        return res.json(CoreApiResponse.success(type.mapFromMulti(response)));
      }
      return res.json(CoreApiResponse.success(type.mapFrom(response)));
    };

    return descriptor;
  };
}

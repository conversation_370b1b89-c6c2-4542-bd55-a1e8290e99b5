import * as ErrorStackParser from 'error-stack-parser';
import { Code } from 'src/core/common';
import { isFunction } from 'src/helpers/utils';
import { LogLevel } from 'src/log';
import { errorLogging, infoLogging } from 'src/middlewares/requestLogging';

import { CoreApiResponse } from '../api';

const parseErrorStack = (error) => {
  if (!error.stack) return [];
  const stackFrames = ErrorStackParser.parse(error);
  return stackFrames
    .filter(
      (frame) =>
        !frame.fileName.includes('handleError.ts') &&
        !frame.source.includes('task_queues') &&
        frame.functionName !== 'rejected' &&
        !frame.fileName.endsWith('.js'),
    )
    .map((frame) => ({
      fileName: frame.fileName,
      functionName: frame.functionName,
      lineNumber: frame.lineNumber,
      columnNumber: frame.columnNumber,
    }));
};

export default function handleError(customMessage: any = '') {
  return function (
    target: object,
    key: string | symbol,
    descriptor: PropertyDescriptor,
  ) {
    const original = descriptor.value;
    descriptor.value = async function (...arguments_: any[]) {
      const [request, res] = arguments_;
      try {
        await original.apply(this, arguments_);
      } catch (error) {
        const userMessage = isFunction(customMessage)
          ? customMessage(request, res)
          : customMessage;
        const beforeMessage =
          userMessage || `${target?.constructor?.name}/${key?.toString()}`;
        const message = error.overrideMessage ?? error.message;
        const excludeStatus = [
          Code.NOT_FOUND.code,
          Code.UNAUTHORIZED_ERROR.code,
        ];
        if (!excludeStatus.includes(error.status)) {
          const messageFrom = error?.isDatabase
            ? 'DatabaseErrorMessage'
            : 'Message';
          const errorData = {
            stack: parseErrorStack(error.overrideError ?? error),
            endpoint: beforeMessage,
            [messageFrom]: message,
          };
          const logLevel = error?.metadata?.logLevel || LogLevel.ERROR;
          if (logLevel === LogLevel.ERROR) {
            errorLogging(request, res, () => {});
          } else {
            infoLogging(request, res, () => {});
          }
          request.logger[logLevel](JSON.stringify(errorData));
        }
        const status = error.status || 500;
        return res
          .status(status)
          .json(CoreApiResponse.error(status, error.message));
      }
    };

    return descriptor;
  };
}

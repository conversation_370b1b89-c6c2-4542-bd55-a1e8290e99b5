export type CodeDescription = {
  code: number;
  message: string;
};

export class Code {
  public static readonly SUCCESS: CodeDescription = {
    code: 200,
    message: 'Success',
  };

  public static readonly BAD_REQUEST_ERROR: CodeDescription = {
    code: 400,
    message: 'Bad request.',
  };

  public static readonly UNAUTHORIZED_ERROR: CodeDescription = {
    code: 401,
    message: 'Unauthorized error.',
  };

  public static readonly ACCESS_DENIED_ERROR: CodeDescription = {
    code: 403,
    message: 'Access denied.',
  };

  public static readonly VALIDATION_ERROR: CodeDescription = {
    code: 422,
    message: 'Invalid request',
  };

  public static readonly NOT_FOUND: CodeDescription = {
    code: 404,
    message: 'Not found',
  };

  public static readonly TOO_MANY_REQUESTS: CodeDescription = {
    code: 429,
    message: 'Too Many Requests',
  };

  public static readonly INVALID_TOKEN: CodeDescription = {
    code: 498,
    message: 'Invalid Token',
  };

  public static readonly INTERNAL_ERROR: CodeDescription = {
    code: 500,
    message: 'Internal error.',
  };

  public static readonly REQUEST_TIMEOUT: CodeDescription = {
    code: 504,
    message: 'Request timeout.',
  };
}

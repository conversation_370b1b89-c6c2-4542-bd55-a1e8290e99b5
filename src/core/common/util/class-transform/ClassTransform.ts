import { classToPlain, plainToClass, serialize } from 'class-transformer';

import { ClassType } from '../../type';

export class ClassTransform {
  public static plainToClass<TTarget extends object>(
    validationClass: ClassType<TTarget>,
    data: object,
  ): object {
    return plainToClass(validationClass, data);
  }

  public static classToPlain<TTarget extends object>(data: TTarget): object {
    return classToPlain(data);
  }

  public static serialize<TTarget extends object>(data: TTarget): string {
    return serialize(data);
  }
}

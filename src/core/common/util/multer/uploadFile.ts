import * as path from 'node:path';

import { MEGABYTE } from 'src/constants';
import { logger } from 'src/log';

import config from '../../../../config';
import { Code } from '../../code/Code';
import { ClientException } from '../../exception';
import { allowFileTypes, mimeByGroup } from '../../mime';
import * as threads from '../workerThreads';

const multer = require('multer');

export const UploadFIleError = multer.MulterError;

const MAX_UPLOAD_FILE_SIZE = 20;
const MAX_FILENAME_SIZE = 100;

const getFiles = (request) => {
  const files = [];
  if (request?.file) {
    files.push(`${path.join(process.cwd(), '/')}${request.file.path}`);
  }
  if (Object.keys(request?.files || [])?.length) {
    for (const items of Object.values(request.files || [])) {
      Object.values(items).forEach((file: { path: string }) => {
        files.push(`${path.join(process.cwd(), '/')}${file.path}`);
      });
    }
  }
  return files;
};

const scanFiles = async (error, request, next) => {
  if (error) next(error);
  if (config.get('upload.virusScan.enabled')) {
    const files = getFiles(request);
    if (files.length > 0) {
      try {
        const scriptName = path.join(__dirname, '../clamscan/index.js');
        const isInfected = await threads.runService(scriptName, { files });
        if (isInfected) throw new Error(`File is infected`);
      } catch (error_) {
        logger.error(`Error scanFiles: ${error_.message}`);
        return next(
          ClientException.new({
            code: Code.BAD_REQUEST_ERROR,
            overrideMessage: 'Error upload file',
          }),
        );
      }
    }
  }
  next();
};

const Upload = (
  data: string | { name: string; maxCount?: number }[],
  {
    maxFileSize = MAX_UPLOAD_FILE_SIZE,
    dest: destination = 'uploads/',
    allowTypes = [mimeByGroup.IMAGE],
    isScanFiles = true,
  }: {
    maxFileSize?: number;
    dest?: string;
    allowTypes?: string[][];
    isScanFiles?: boolean;
  } = {},
) => {
  const upload = multer({
    dest: destination,
    limits: {
      fileSize: maxFileSize
        ? maxFileSize * MEGABYTE
        : MAX_UPLOAD_FILE_SIZE * MEGABYTE,
    },
    fileFilter: (request, file, callback) => {
      const types = allowTypes.reduce(
        (item, accumulator) => accumulator.concat(item),
        [],
      );
      const extname = allowFileTypes.test(
        path.extname(file.originalname).toLowerCase(),
      );

      if (file.originalname.length > MAX_FILENAME_SIZE) {
        return callback(
          ClientException.new({
            code: Code.VALIDATION_ERROR,
            overrideMessage: 'File name too large',
          }),
        );
      }
      if (!types.includes(file.mimetype) || !extname) {
        return callback(
          ClientException.new({
            code: Code.VALIDATION_ERROR,
            overrideMessage: 'File should be correct format',
          }),
        );
      }
      return callback(null, true);
    },
  });

  return (request, res, next) => {
    if (Array.isArray(data)) {
      return upload.fields(data)(request, res, (error) => {
        if (isScanFiles) return scanFiles(error, request, next);
        return next();
      });
    }
    return upload.single(data)(request, res, (error) => {
      if (isScanFiles) return scanFiles(error, request, next);
      return next();
    });
  };
};

export default Upload;

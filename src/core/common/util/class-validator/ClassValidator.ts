import { validate, ValidationError } from 'class-validator';

import { ClassType, Optional } from '../../type';
import { ClassTransform } from '../class-transform/ClassTransform';

export type ClassValidationDetails = {
  context: string;
  errors: ClassValidationErrors[];
};

export type ClassValidationErrors = {
  property: string;
  message: string[];
};

export class ClassValidator {
  public static async validate<DTO extends object, TTarget extends object>(
    dto: ClassType<DTO>,
    target: TTarget,
    context?: string,
  ): Promise<Optional<ClassValidationDetails>> {
    let details: Optional<ClassValidationDetails>;
    const errors: ValidationError[] = await validate(
      ClassTransform.plainToClass<DTO>(dto, target),
    );

    if (errors.length > 0) {
      details = {
        context: context || target.constructor.name,
        errors: [],
      };
      for (const error of errors) {
        details.errors.push({
          property: error.property,
          message: error.constraints ? Object.values(error.constraints) : [],
        });
      }
    }

    return details;
  }
}

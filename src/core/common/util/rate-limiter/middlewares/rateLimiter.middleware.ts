import { RateLimiterRedis as RateLimiter } from 'rate-limiter-flexible';
import * as redis from 'redis';
import { MINUTES } from 'src/constants';
import { Code, CoreApiResponse } from 'src/core/common';

const redisClient = redis.createClient({
  url: process.env.REDISCLOUD_URL,
});

const rateLimiter = ({
  points = 10,
  duration = 30,
  blockDuration = 0,
  keyPrefix = 'default',
}: {
  points?: number;
  duration?: number;
  blockDuration?: number;
  keyPrefix?: string;
}) => {
  const rateLimiterInstance = new RateLimiter({
    storeClient: redisClient,
    keyPrefix: `middleware:${keyPrefix}`,
    points,
    duration: duration,
    blockDuration: blockDuration * MINUTES,
  });

  return (request, res, next) => {
    rateLimiterInstance
      .consume(request.ip)
      .then(() => {
        next();
      })
      .catch(() => {
        res
          .status(Code.TOO_MANY_REQUESTS.code)
          .json(
            CoreApiResponse.error(
              Code.TOO_MANY_REQUESTS.code,
              `Too many attempts. Please try again later`,
            ),
          );
      });
  };
};

export default rateLimiter;

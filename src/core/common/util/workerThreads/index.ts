const { Worker } = require('node:worker_threads');

const runService = (filePath, data) => {
  return new Promise((resolve, reject) => {
    const worker = new Worker(filePath, { workerData: data });
    worker.on('message', resolve);
    worker.on('error', reject);
    worker.on('exit', (code) => {
      if (code !== 0) reject(new Error(`stopped with  ${code} exit code`));
    });
  });
};

export { runService };

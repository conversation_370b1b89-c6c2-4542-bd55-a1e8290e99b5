export function getInitials(name?: string) {
  let initials = '';
  if (!name.includes(' ')) {
    initials = name?.charAt(0);
  } else {
    initials = `${name.split(' ')[0][0]}${name.split(' ')[1][0]}`;
  }

  return initials.toUpperCase();
}

export function getAvatarWithInitials(name?: string) {
  return `<div style="width: 40px; height: 40px; background-color: #EAF0FF; color: #2055FF; font-weight: 500; font-size: 16px; line-height: 40px; text-align: center; border-radius: 50%; font-family: Arial, sans-serif; display: inline-block; vertical-align: middle;">
  ${getInitials(name || 'User')}</div>`;
}

export function getSocialLinksMarkUp() {
  return `<a href="https://www.facebook.com/treedismsp" style="display: inline-block; margin: 0 10px;"><img src="https://plugin.markaimg.com/public/3827a3bf/ytEZf72LLo91NhN9Ux06ucDCOgKwv6.png" style="width: 32px; height: auto; display: block;" alt="Facebook" /></a><a href="https://www.facebook.com/groups/381455879490087" style="display: inline-block; margin: 0 10px;"><img src="https://plugin.markaimg.com/public/3827a3bf/8fmYdEfqtQ6TXluhWwvHHXBte6p1G8.png" style="width: 32px; height: auto; display: block;" alt="Facebook Group" /></a><a href="https://il.linkedin.com/company/treedis" style="display: inline-block; margin: 0 10px;"><img src="https://plugin.markaimg.com/public/3827a3bf/yjgm28tNi5gjWzyfJsPQRP6EB0WZRH.png" style="width: 32px; height: auto; display: block;" alt="LinkedIn" /></a>`
}

export function getCopyRightMarkup() {
  return `
    <span style="color: #000; font-family: Poppins, Arial, sans-serif; font-size: 11px;">Copyright © 2024 Treedis, All rights reserved.</span>
    `
}

export function tranformNoteMessage(message: string): string {
  const pTagRegex = /<p([^>]*)>/g;
  const processedMessage = message
    .replace(pTagRegex, (match, p1) => {
      return `<p${p1} style="color: #475467;">`;
    });

  const spanRegex = /<span([^>]*)style="([^"]*)"([^>]*)>(.*?)<\/span>/g;
  const tranformedMessage = processedMessage.replace(spanRegex, (match, p1, p2, p3, p4) => {
    const newStyle = p2.replace(/color:\s*[^;]+;/g, '') + ' background-color: #F2F4F7; border-radius: 6px; padding: 4px 8px; line-height: 1.5';
    return `<span${p1}style="${newStyle}"${p3}>${p4}</span>`;
  });

  return tranformedMessage;
}

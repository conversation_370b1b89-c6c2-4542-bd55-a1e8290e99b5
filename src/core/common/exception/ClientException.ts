import { CodeDescription } from '../code/Code';
import { Optional } from '../type';
import { BaseException } from './BaseException';
import {LogLevel} from "src/log";

type ExceptionMetaData<TData extends unknown> = {
  data?: TData;
  logLevel?: LogLevel;
  stack?: unknown;
}

export type CreateExceptionPayload<TData> = {
  code?: CodeDescription;
  overrideMessage?: string;
  metadata?: ExceptionMetaData<TData>;
};

export class ClientException<TData> extends BaseException {
  public readonly status: number;

  public readonly metadata: Optional<ExceptionMetaData<TData>>;

  private constructor(
    codeDescription: CodeDescription,
    overrideMessage?: string,
    metadata?: ExceptionMetaData<TData>,
  ) {
    super();

    this.name = this.constructor.name;
    this.status = codeDescription.code;
    this.metadata = metadata;
    this.message = overrideMessage || codeDescription.message;

    Error.captureStackTrace(this, this.constructor);
  }

  public static new<TData>(
    payload: CreateExceptionPayload<TData>,
  ): ClientException<TData> {
    return new ClientException(
      payload.code,
      payload.overrideMessage,
      payload.metadata,
    );
  }

  public static isClientException(error: unknown): error is ClientException<unknown> {
    return error instanceof ClientException;
  }
}

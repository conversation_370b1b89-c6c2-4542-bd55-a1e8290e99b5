import * as process from "node:process";

import { get } from "lodash";
// import { secretManager } from 'src/core/service';

export interface Config {
  [key: string]: any;
}

class ConfigService {
  private static instance: ConfigService;

  private static config: Config;

  private constructor() {}

  /**
   * @param configVariables
   */
  public static getInstance(configVariables: Config): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.setConfigData(configVariables);
      ConfigService.instance = new ConfigService();
    }

    return ConfigService.instance;
  }

  public static rewriteConfig(configVariables: Config): void {
    ConfigService.setConfigData(configVariables);
  }

  /**
   * Get the param or use default
   *
   * @param parameter
   * @param {any} value default
   * @returns {any|undefined}
   */
  static get(parameter: string | string[], value: unknown = null): any {
    const configValue = get(ConfigService.config, parameter);
    if (configValue === undefined) {
      return value;
    }
    return configValue;
  }

  static getWithArgs(parameter: string | string[], ...arguments_: any[]): any {
    const configValue = get(ConfigService.config, parameter);
    if (configValue === undefined) {
      return undefined;
    }
    return configValue(...arguments_);
  }

  /**
   * Check the param exists
   *
   * @param param
   * @returns {boolean}
   */
  static has(parameter: string | string[]): boolean {
    return get(ConfigService.config, parameter) !== undefined;
  }

  private static setConfigData(configVariables: Config): void {
    ConfigService.config = configVariables;
  }

  public static async getConfig(): Promise<void> {
    const secretName = `${process.env.AWS_ENV || "dev"}/server`;
    const isOverrideEnvironmentToSSM =
      process.env.OVERRIDE_ENV_TO_SSM === "true";
    const parameters = {}; //await secretManager.get(secretName);
    for (const key of Object.keys(parameters)) {
      if (
        !isOverrideEnvironmentToSSM ||
        (isOverrideEnvironmentToSSM && process.env[key] === undefined)
      ) {
        process.env[key] = parameters[key];
      }
    }
  }
}

export default ConfigService;

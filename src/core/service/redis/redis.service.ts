import { promisify } from 'node:util';

import { createClient } from 'redis';
import config from 'src/config';
import { logger } from 'src/log';

export class RedisService {
  private readonly client: any;

  constructor() {
    this.client = createClient({
      url: config.get('redis.url'),
    });
    this.client.on('error', (error) =>
      logger.error('Redis Client Error', error),
    );
  }

  public async set(key: string, value: string): Promise<void> {
    const setAsync = promisify(this.client.set).bind(this.client);
    await setAsync(key, value);
  }

  public async get(key: string): Promise<string | null> {
    const getAsync = promisify(this.client.get).bind(this.client);
    return await getAsync(key);
  }

  public async delete(key: string): Promise<void> {
    const delAsync = promisify(this.client.del).bind(this.client);
    await delAsync(key);
  }

  public async disconnect(): Promise<void> {
    await this.client.disconnect();
  }
}

import * as path from 'node:path';

import { uuid } from 'uuidv4';

import { MEGABYTE } from '../../constants';
import { ClientException, Code, Dictionary, mime } from '../common';

export interface IFileAdapterPort {
  upload(
    name: any,
    filePath: string,
    folder: string,
    options?: Dictionary<string>,
  ): Promise<string>;

  deleteFile(name: string): Promise<string>;
}

export type FileAdapterOptions = {
  uploadPath: string;
  allowedTypes?: any[];
  fileSize?: number;
};

export interface IFileAdapter {
  upload(
    file: Buffer | NodeJS.ReadableStream,
    options?: FileAdapterOptions,
  ): Promise<string>;

  uploadFromPath(file: string, destination: string): Promise<string>;

  delete(fileName: string, filePath: string): Promise<any>;

  createFileName(path: string): string;
}

export class FileAdapter<T extends IFileAdapterPort> implements IFileAdapter {
  private adapter: T;

  constructor(_adapter: T) {
    this.adapter = _adapter;
  }

  public async upload(
    file: any,
    { uploadPath, allowedTypes = [], fileSize = 10 },
  ) {
    const extension = path.extname(file.originalname);

    if (!file) {
      throw ClientException.new({
        overrideMessage: 'File is empty',
        code: Code.VALIDATION_ERROR,
      });
    }

    if (file.size > fileSize * MEGABYTE) {
      throw ClientException.new({
        overrideMessage: `File should be not more ${fileSize} Mb`,
        code: Code.VALIDATION_ERROR,
      });
    }

    if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
      throw ClientException.new({
        overrideMessage: 'File should be of correct format',
        code: Code.VALIDATION_ERROR,
      });
    }

    const fileName = uuid();

    const uploadFileName = await this.adapter.upload(
      `${fileName}${extension}`,
      file.path,
      uploadPath,
      {
        ContentType: mime[extension.slice(1)],
      },
    );
    return uploadFileName;
  }

  public async uploadFromPath(filePath, destination) {
    const extension = path.extname(filePath);
    const uploadFileName = await this.adapter.upload(
      `${this.createFileName(filePath)}`,
      filePath,
      destination,
      {
        ContentType: mime[extension.slice(1)],
      },
    );
    return uploadFileName;
  }

  public async delete(fileName: string, filePath: string) {
    try {
      await this.adapter.deleteFile(`${filePath}/${fileName}`);
      return true;
    } catch (error) {
      throw ClientException.new({
        overrideMessage: `Error delete file ${fileName}: ${error.message}`,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  public createFileName(filePath) {
    const extension = path.extname(filePath);
    const fileName = uuid();
    return `${fileName}${extension}`;
  }
}

import { get } from 'env-var';

export const MEGABYTE = 1024 * 1024;
export const MINUTES = 60;
export const HOUR = MINUTES * 60;

export const RESOLUTION = {
  HD: [1280, 720],
  FULL_HD: [1920, 1080],
  QHD: [2560, 1440],
};

export const EMAILS = {
  signup: {
    free: '36041924', // Free Welcome Email { name }
    growth: '36041875', // Growth Welcome Email { name }
    business: '36041903', // Business Welcome Email Figma { name }
  },
  features: {
    // Features Figma { name } .. one email for all for now
    free: '36069077',
    growth: '36069077',
    business: '36069077',
    onDay: 2,
    key: 'features',
  },
  immersiveExperience: {
    // Immersive Experience Figma .. one email for all for now
    free: '36082336',
    growth: '36082336',
    business: '36082336',
    onDay: 5,
    key: 'immersiveExperience',
  },
  whiteLabel: {
    // white label email { name } .. one email for all for now
    free: '36069078',
    growth: '36069078',
    business: '36069078',
    onDay: 7,
    key: 'whiteLabel',
  },
  connectedWorkers: {
    // Connected workers Figma .. one email for all for now
    free: '36082146',
    growth: '36082146',
    business: '36082146',
    onDay: 10,
    key: 'connectedWorkers',
  },
  caseStudies: {
    // Case Studies Figma { name } .. one email for all for now
    free: '36069079',
    growth: '36069079',
    business: '36069079',
    onDay: 12,
    key: 'caseStudies',
  },
  keepingPace: {
    // Keeping pace with developments Figma .. one email for all for now
    free: '36069204',
    growth: '36069204',
    business: '36069204',
    onDay: 18,
    key: 'keepingPace',
  },
  trialCancel: '36069096', // Canceled trail Figma { name }
  // no card
  trialExpired: {
    growth: '36069059', // Growth expired Figma { name, coupon }
    business: '36069084', // Business expired Figma { name, coupon }
  },
  emailConfirm: '36069061',
  popupFreeSubscribers: '36069057', // Pop Up Email for Free Subscribers { name, coupon }
};
export const STRIPE_PLAN_IDS = {
  business_month: get('SUB_BUSINESS_MONTH').asString(),
  business_year: get('SUB_BUSINESS_YEAR').asString(),
  growth_month: get('SUB_GROWTH_MONTH').asString(),
  growth_year: get('SUB_GROWTH_YEAR').asString(),
  enterprise: get('SUB_ENTERPRISE_YEAR').asString(),
};

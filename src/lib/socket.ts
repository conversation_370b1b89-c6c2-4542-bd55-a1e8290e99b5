import * as cluster from 'node:cluster';
import { Server } from 'node:http';

import { createAdapter } from '@socket.io/redis-adapter';
import { setupWorker } from '@socket.io/sticky';
import { createClient } from 'redis';
import { logger } from 'src/log';

import { SocketService } from '../core/service';

export async function createSocket(server: Server) {
  try {
    new SocketService(server, {
      transports: ['websocket'],
      cors: {
        origin: '*',
      },
    });

    const io = SocketService.getIO();
    const pubClient = createClient({
      url: process.env.REDISCLOUD_URL,
    });
    const subClient = pubClient.duplicate();

    io.adapter(createAdapter(pubClient, subClient));

    // @ts-ignore
    if (cluster.isWorker) {
      logger.info('Setting sticky session');
      setupWorker(io);
    }

    return io;
  } catch (error) {
    logger.error(`Socket init error: ${error.message}`);
  }
}

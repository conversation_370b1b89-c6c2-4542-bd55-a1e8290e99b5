// import config from "src/config";
// import { logger } from "src/log";

// import { ClientException, Code, Role } from "../core/common";

// const jwt = require("jsonwebtoken");

// type JWTUserDecode = {
//   userId: number;
//   userType: UserType;
// };

// const AUTHORIZATION_HEADER = "Authorization";
// const HOST_HEADER = "host";

// export async function checkRole(role: Role, decoded, headers: any[] = []) {
//   if (!decoded.userId) {
//     throw ClientException.new({
//       code: Code.UNAUTHORIZED_ERROR,
//     });
//   }

//   switch (role) {
//     case Role.SuperAdmin:
//     case Role.Admin:
//     case Role.User: {
//       if (decoded.userType !== role) {
//         return null;
//       }
//       // authorized as admin

//       /** replacing the admins id to a users id
//        * allows admin to edit tours as a user */
//       let id = decoded.userId;
//       const referer = headers ? headers["treedis-url"] : null;
//       if (
//         referer &&
//         (decoded.userType === Role.SuperAdmin ||
//           decoded.userType === Role.Admin)
//       ) {
//         const path = referer.match(/\/users\/(?<userId>\d+)\//);
//         if (path?.groups?.userId) id = path.groups.userId;
//       }

//       return {
//         type: decoded.userType,
//         id: id,
//         selfId: decoded.userId,
//       };
//     }
//     case Role.Client: {
//       if (decoded.userType !== Role.Client) {
//         return null;
//       }
//       // authorized as client
//       const client = await new DbClient().fromPk(decoded.userId);

//       if (client.status === "disabled") {
//         logger.info(`Client id: ${client.id} has status disabled`);
//         throw ClientException.new({
//           code: Code.ACCESS_DENIED_ERROR,
//         });
//       }
//       return {
//         type: decoded.userType,
//         id: client.userId,
//         selfId: decoded.userId, // actually client id
//       };
//     }
//     case Role.Agent: {
//       if (decoded.userType !== Role.Agent) {
//         return null;
//       }
//       // authorized as agent
//       const agent = await new DbAgent().fromId(decoded.userId);

//       return {
//         type: decoded.userType,
//         id: agent.userId,
//         selfId: decoded.userId, // actualy agent id
//       };
//     }
//     case Role.Guest: {
//       if (decoded.userType !== Role.Guest) {
//         return null;
//       }

//       return {
//         type: decoded.userType,
//         id: decoded.userId,
//         name: decoded.name,
//         email: decoded.email,
//       };
//     }
//     default: {
//       throw ClientException.new({
//         code: Code.UNAUTHORIZED_ERROR,
//       });
//     }
//   }
// }

// export const tourHasAccess = async ({ type: role, id, selfId }, tourId) => {
//   const tourWithRoutes = await new DbTour().fromPkWithRoles(tourId);
//   if (
//     role === Role.SuperAdmin ||
//     ((role === Role.Admin || role === Role.User) &&
//       tourWithRoutes.users.some(
//         (user) => user.id === id || user.adminId === selfId
//       )) ||
//     (role === Role.Client && tourWithRoutes.client?.id === selfId) ||
//     (role === Role.Agent &&
//       tourWithRoutes.agentTours.some((agent) => agent.agentId === selfId))
//   ) {
//     return true;
//   }
//   if (role === Role.Agent) {
//     const agent = await new DbAgent().fromId(selfId);
//     if (
//       tourWithRoutes.client?.id === agent.clientId &&
//       agent.permissionType === AgentPermissionType.Editor
//     ) {
//       return true;
//     }
//   }
//   logger.info(
//     `User id: ${id}, selfId: ${selfId}, role: ${role} has not access to this endpoint`
//   );
//   throw ClientException.new({
//     code: Code.ACCESS_DENIED_ERROR,
//   });
// };

// const getUrl = (request) =>
//   `${request.protocol}://${request.get(HOST_HEADER)}${request.originalUrl}`;

// const verifyJWTToken = (token: string): JWTUserDecode => {
//   const JWT_SECRET = config.get("jwt.secret");
//   try {
//     return jwt.verify(token, JWT_SECRET);
//   } catch (error) {
//     throw ClientException.new({
//       code: Code.UNAUTHORIZED_ERROR,
//       overrideMessage: error.message,
//     });
//   }
// };

// export default function authorize(roles: Array<Role>) {
//   return function (
//     target: object,
//     key: string | symbol,
//     descriptor: PropertyDescriptor
//   ) {
//     const original = descriptor.value;

//     descriptor.value = async function (request, res, next) {
//       try {
//         if (request.method !== "OPTIONS") {
//           let account: any;
//           const decoded = verifyJWTToken(request.get(AUTHORIZATION_HEADER));
//           for await (const role of roles) {
//             const result = await checkRole(role, decoded, request.headers);
//             if (result) {
//               account = result;
//             }
//           }

//           if (!account) {
//             logger.info(
//               `User (id: ${decoded.userId}, role: ${
//                 decoded.userType
//               }) has not access to ${getUrl(request)}`
//             );
//             throw ClientException.new({
//               code: Code.ACCESS_DENIED_ERROR,
//             });
//           }
//           request.account = account;
//         }

//         const result = await Reflect.apply(original, this, [
//           request,
//           res,
//           next,
//         ]);
//         return result;
//       } catch (error) {
//         if (error instanceof ClientException) {
//           return res.status(error.status).json({
//             message: error.message,
//           });
//         }
//         return res.status(error.status || Code.BAD_REQUEST_ERROR.code).json({
//           message: Code.BAD_REQUEST_ERROR.message,
//         });
//       }
//     };

//     return descriptor;
//   };
// }

// export { Role } from "../core/common";

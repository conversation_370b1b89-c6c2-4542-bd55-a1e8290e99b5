import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class SaveSSOSettingsRequest {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Expose()
  clientId: number;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Expose()
  clientSecret: string;

  @IsOptional()
  @IsString()
  @IsNotEmpty()
  @Expose()
  domain: string;

  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  @Expose()
  isEnabled: boolean;

  @IsOptional()
  @IsNumber()
  @Expose()
  providerId: number;

  @IsOptional()
  @IsArray()
  @Expose()
  allowSSODomains: Array<string>;

  @IsOptional()
  @IsString()
  @Expose()
  spPrivateKey: string;

  @IsOptional()
  @IsString()
  @Expose()
  spCertificate: string;

  @IsOptional()
  @IsString()
  @Expose()
  idpLoginUrl: string;

  @IsOptional()
  @IsString()
  @Expose()
  idpLogoutUrl: string;

  @IsOptional()
  @IsString()
  @Expose()
  idpCertificate: string;
}

import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';

import { IsPasswordValid } from '../../../core/common/util/class-validator/decorators';

export class ResetPasswordRequest {
  @IsString()
  @MaxLength(100)
  @IsNotEmpty()
  @Expose()
  passwordToken: string;

  @IsString()
  @MinLength(6)
  @MaxLength(50)
  @IsPasswordValid()
  @IsNotEmpty()
  @Expose()
  newPassword: string;

  @IsString()
  @IsNotEmpty()
  @Expose()
  captchaToken: string;
}

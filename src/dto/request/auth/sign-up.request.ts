import { Expose } from "class-transformer";
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
} from "class-validator";

import { Match } from "../../../core/common/util/class-validator/decorators";

enum UserSignupMethod {
  Signup = "signup",
  Manual = "manual",
  Referral = "referral",
}

export class SignUpRequest {
  @IsString()
  @Expose()
  firstName: string;

  @IsString()
  @Expose()
  lastName: string;

  @IsString()
  @MinLength(4)
  @MaxLength(50)
  @IsNotEmpty()
  @IsEmail()
  @Expose()
  email: string;

  @IsString()
  @MaxLength(50)
  @IsOptional()
  @Expose()
  company: string;

  @IsString()
  @MinLength(2)
  @MaxLength(50)
  @IsOptional()
  @Expose()
  country: string;

  @IsString()
  @MinLength(4)
  @MaxLength(50)
  @IsOptional()
  @Expose()
  phone: string;

  @IsString()
  @IsOptional()
  @Expose()
  refer: string;

  @IsString()
  @Expose()
  password: string;

  @IsString()
  @Match("password")
  @Expose()
  validatePassword: string;

  @IsString()
  // @IsNotEmpty()
  @IsOptional()
  @Expose()
  captchaToken: string;

  @IsString()
  @IsOptional()
  @Expose()
  numberOfSpaces: string;

  @IsString()
  @IsOptional()
  @Expose()
  numberOfEmployees: string;

  @IsOptional()
  @IsEnum(UserSignupMethod)
  @Expose()
  signupMethod: UserSignupMethod;

  @IsString()
  @IsOptional()
  @Expose()
  googleId: string;
}

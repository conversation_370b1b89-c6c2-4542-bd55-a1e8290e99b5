import { Expose, plainToClass } from 'class-transformer';
import { IsEmail, IsInt, IsString } from 'class-validator';

export class UserResponse {
  @Expose()
  @IsInt()
  id: number;

  @Expose()
  @IsString()
  name: string;

  @Expose()
  @IsEmail()
  email: string;

  public static mapFrom<P>(data: Required<P>): UserResponse {
    return plainToClass(UserResponse, data, { excludeExtraneousValues: true });
  }
}

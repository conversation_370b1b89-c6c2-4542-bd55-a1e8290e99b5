import { Expose, plainToClass } from 'class-transformer';
import { IsInt } from 'class-validator';

export class GetUserAdminResponse {
  @Expose()
  @IsInt()
  id: number;

  @Expose()
  @IsInt()
  twoDFloorPlanPrice: number;

  @Expose()
  @IsInt()
  twoDFloorPlanCadFilePrice: number;

  @Expose()
  @IsInt()
  twoDFloorPlanTwoDColorPrice: number;

  @Expose()
  @IsInt()
  twoDFloorPlanFloors: number;

  @Expose()
  @IsInt()
  threeDFloorPlanFloors: number;

  @Expose()
  @IsInt()
  threeDFloorPlanThreeDColorPrice: number;

  @Expose()
  @IsInt()
  twoDSixHoursDelivery: number;

  @Expose()
  @IsInt()
  twoDFourteenHoursDelivery: number;

  @Expose()
  @IsInt()
  sitePlanPrice: number;

  public static mapFrom<P>(data: Required<P>): GetUserAdminResponse {
    return plainToClass(GetUserAdminResponse, data, {
      excludeExtraneousValues: true,
    });
  }
}

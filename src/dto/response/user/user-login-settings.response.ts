import { Expose, plainToClass } from 'class-transformer';

export class UserLoginSettingsResponse {
  @Expose()
  userId: number;

  @Expose()
  title: string;

  @Expose()
  description: string;

  @Expose()
  primaryColor: string;

  @Expose()
  secondaryColor: string;

  @Expose()
  backgroundColor: string;

  @Expose()
  textColor: string;

  @Expose()
  enableRight: string;

  @Expose()
  rightDescription: string;

  @Expose()
  overlayColor: string;

  @Expose()
  fontColor: string;

  @Expose()
  logoUrl: string;

  @Expose()
  backgroundUrl: string;

  public static mapFrom<P>(data: Required<P>): UserLoginSettingsResponse {
    return plainToClass(UserLoginSettingsResponse, data, {
      excludeExtraneousValues: true,
    });
  }
}

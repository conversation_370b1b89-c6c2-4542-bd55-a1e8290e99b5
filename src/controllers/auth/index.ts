import { <PERSON>b<PERSON><PERSON>, User } from "../../db/lib/user/user";
import { Client } from "@hubspot/api-client";
// import {
//   DbAgent,
//   DbClient,
//   DbEmailSchedules,
//   DbUser,
//   User,
//   UserType,
// } from "@treedis/db-access";
import StripePayment from "src/core/service/stripe";
import { BotQueue } from "src/queues";
import { uuid } from "uuidv4";

import config from "../../config";
import { EMAILS, MINUTES } from "../../constants";
import { ClientException, Code, EnumQueue, Role } from "../../core/common";
import { rateLimiter } from "../../core/common/util";
import { getHost, getToursLimit } from "../../helpers";
import { createTokenJWT, getRandomNumber } from "../../helpers/utils";
import { logger, LogLevel } from "../../log";
// import {
//   addMailchimp,
//   alreadyRegistered,
//   subscribeMailchimp,
// } from '../../routers/util';
// import FolderController from '../folder';
// import TourController from '../tour';

const jwt = require("jsonwebtoken");

const ct = require("countries-and-timezones");
const passwordHash = require("password-hash");

const TWO_FA_RANDOM_NUMBER = 6;

class AuthController {
  queues;

  botQueue: BotQueue;

  stripe: StripePayment;

  // folderController: FolderController;

  constructor(queues) {
    this.queues = queues;
    // this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    // this.folderController = new FolderController();
    this.botQueue = new BotQueue();
    // this.tourController = new TourController({
    //   [EnumQueue.Bot]: this.botQueue,
    // });
  }

  // private async updateHubspotContact(
  //   email: string,
  //   properties: Record<string, any>,
  //   skipCreate?: boolean
  // ) {
  //   try {
  //     const hubspotClient = new Client({
  //       accessToken: process.env.HUBSPOT_API_KEY,
  //     });

  //     try {
  //       await hubspotClient.crm.contacts.basicApi.update(
  //         email,
  //         { properties },
  //         "email"
  //       );
  //       logger.info(`Hubspot contact ${email} updated`);
  //     } catch (error) {
  //       if (error.body.message === "resource not found" && !skipCreate) {
  //         // Failed to update contact which means it doesn't exist in Hubspot and we create it
  //         await hubspotClient.crm.contacts.basicApi.create({
  //           properties: { email: email, ...properties },
  //         });
  //         logger.info(`Hubspot contact ${email} created`);
  //       } else {
  //         throw error;
  //       }
  //     }
  //   } catch (error) {
  //     logger.info(`Failed to update Hubspot contact ${error.message}`);
  //   }
  // }

  // private async handleEmailSchedules(userId: number) {
  //   try {
  //     console.log("Handling email schedules for user:", userId);
  //     const emailSchedules = await new DbEmailSchedules();

  //     // if prod then 1 unit is 1 day and if dev then 1 unit is 1 hour
  //     const unit =
  //       process.env.NODE_ENV === "production"
  //         ? 24 * 60 * 60 * 1000
  //         : 60 * 60 * 1000;

  //     const emails = Object.values(EMAILS) as [
  //       {
  //         free: string;
  //         growth: string;
  //         business: string;
  //         key: string;
  //         onDay?: number;
  //       }
  //     ];

  //     for (const email of emails) {
  //       if (email.onDay) {
  //         emailSchedules.addEntry({
  //           sendDate: new Date(Date.now() + email.onDay * unit),
  //           postmarkFreeId: email.free,
  //           postmarkGrowthId: email.growth,
  //           postmarkBusinessId: email.business,
  //           key: email.key,
  //           userId,
  //         });
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Error handling email schedules:", error);
  //   }
  // }

  public async signup(data, originDomain: string) {
    try {
      const origin = getHost(originDomain);
      const domains = ["gridevo.com"];
      if (!domains.includes(origin) && process.env.NODE_ENV === "production") {
        throw ClientException.new({
          overrideMessage: "Sign up is not allowed from this domain",
          code: Code.BAD_REQUEST_ERROR,
        });
      }

      const user = new DbUser();
      Object.assign(user, data);

      user.email = data.email.toLowerCase();
      user.password = passwordHash.generate(user.password);
      // const emailToken = uuid();
      // user.emailToken = emailToken;
      // user.isNewAdmin = true;
      // user.subscription = data.AWSIdentifier ? "enterprise" : "free";
      // user.toursLimit = getToursLimit(user.subscription);

      if (await user.checkExisting(user.email)) {
        throw ClientException.new({
          overrideMessage: "User with this email already exists",
          code: Code.BAD_REQUEST_ERROR,
        });
      }

      // let refererUser;
      // if (data.refer) {
      //   refererUser = await new DbUser().fromUsername(data.refer);
      //   user.refer = refererUser ? refererUser.id : null;
      // }
      console.log("BEFORE SAVE!!!");
      await user.save();

      // if (refererUser) {
      //   await this.queues[EnumQueue.Email].sendEmail(
      //     "referUser",
      //     config.get("mail.from"),
      //     refererUser.email,
      //     {
      //       name: user.name,
      //       email: user.email,
      //       phone: user.phone,
      //       country: user.country,
      //       company: user.company,
      //       mode: user.mode,
      //     }
      //   );
      // }

      // await (user.country === "IL"
      //   ? this.queues[EnumQueue.Email].sendEmail(
      //       "israelSignup",
      //       config.get("mail.from"),
      //       "<EMAIL>",
      //       {
      //         name: user.name,
      //         email: user.email,
      //         company: user.company,
      //         phone: user.phone,
      //         token: emailToken,
      //       }
      //     )
      //   : this.queues[EnumQueue.Email].sendEmail(
      //       "confirmEmail",
      //       config.get("mail.from"),
      //       user.email,
      //       {
      //         name: user.name,
      //         token: emailToken,
      //       }
      //     ));

      // Upsert user in Hubspot
      // if (process.env.NODE_ENV === "production") {
      //   const properties = {
      //     firstname: data.firstName,
      //     lastname: data.lastName,
      //     full_name: data.firstName + " " + data.lastName,
      //     company: data.company,
      //     country: data.country,
      //     phone: data.phone,
      //     is_matterport_service_provider: data.mpServiceProvider ? "yes" : "no",
      //     numemployees: data.numberOfEmployees || 0,
      //     industry_new: data.industry || "",
      //     website: data.website || "",
      //     job_description: data.jobDescription || "",
      //     existing_3d_digital_twin: data.existing3DDigitalTwin ? "yes" : "no",
      //     hear_place: data.hearPlace || "",
      //     newsletter_enabled: data.newsletterEnabled ? "yes" : "no",
      //     treedis_admin_sign_up: "yes",
      //     treedis_sign_up_date: new Date().setUTCHours(0, 0, 0, 0),
      //   };

      //   // Updating hubspot contact
      //   // await this.updateHubspotContact(data.email, properties);
      // }

      // await this.handleEmailSchedules(user.id);

      if (process.env.NODE_ENV !== "production") {
        return {
          // confirmEmailURL: `http://localhost:3000/admin/confirm-email/${emailToken}`,
        };
      }

      return true;
    } catch (error) {
      console.log("error!!!!!", error);
      logger.error(`auth/signup: ${error.overrideMessage || error.message}`);
      if (error instanceof ClientException) throw error;
      throw ClientException.new({
        overrideMessage: error.message,
        code: Code.BAD_REQUEST_ERROR,
      });
    }
  }

  // public async updateEmail(userId, newEmail) {
  //   try {
  //     // async fromId(id: number, isNewAdmin: boolean = false)
  //     const user = await new DbUser().fromId(userId, true);
  //     const oldEmail = user.email;

  //     if (!user) {
  //       throw ClientException.new({
  //         overrideMessage: "User not found",
  //         code: Code.BAD_REQUEST_ERROR,
  //       });
  //     }

  //     // If a user with the new email exists
  //     if (userId === 1) {
  //       throw ClientException.new({
  //         overrideMessage: "Super admin email cannot be changed",
  //         code: Code.BAD_REQUEST_ERROR,
  //       });
  //     }

  //     // Check if the new email is already registered
  //     const userFromNewEmail = await new DbUser()
  //       .fromEmail(newEmail)
  //       .catch(() => ({}));

  //     // If a user with the new email exists
  //     if (userFromNewEmail.id) {
  //       throw ClientException.new({
  //         overrideMessage: "Email address is already taken",
  //         code: Code.BAD_REQUEST_ERROR,
  //       });
  //     }

  //     // Check if the new email is the same as the old one
  //     if (userFromNewEmail.id === userId) {
  //       throw ClientException.new({
  //         overrideMessage: "Email address is the same as the old one",
  //         code: Code.BAD_REQUEST_ERROR,
  //       });
  //     }

  //     const emailToken = uuid();

  //     // Update user's email, emailToken, and confirmed status
  //     user.email = newEmail.toLowerCase();
  //     user.emailToken = emailToken;
  //     user.confirmed = false;
  //     await user.save();

  //     await this.stripe.updateCustomer(user.stripeId, {
  //       name: user.name,
  //       email: newEmail.toLowerCase(),
  //     });

  //     await this.updateHubspotContact(
  //       oldEmail,
  //       {
  //         email: newEmail.toLowerCase(),
  //       },
  //       true
  //     );

  //     // Send confirmation email to the new address
  //     await this.queues[EnumQueue.Email].sendEmail(
  //       "updateEmail",
  //       config.get("mail.from"),
  //       newEmail,
  //       {
  //         name: user.name,
  //         token: emailToken,
  //       }
  //     );

  //     if (process.env.NODE_ENV !== "production") {
  //       return {
  //         confirmEmailURL: `http://localhost:3000/admin/confirm-email/${emailToken}`,
  //       };
  //     }

  //     return true;
  //   } catch (error) {
  //     if (error instanceof ClientException) throw error;

  //     throw ClientException.new({
  //       overrideMessage: "Failed to update email address",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // // public async ssoLogin(token: string, type: UserType) {
  // //   if (!token) {
  // //     throw ClientException.new({
  // //       overrideMessage: "Missing SSO token",
  // //       code: Code.ACCESS_DENIED_ERROR,
  // //     });
  // //   }
  // //   const decoded = jwt.decode(token);
  // //   const email = decoded?.email?.toLowerCase();
  // //   if (!email) {
  // //     throw ClientException.new({
  // //       overrideMessage: "Incorrect SSO token",
  // //       code: Code.INVALID_TOKEN,
  // //     });
  // //   }

  // //   let data = { user: null, token: null };
  // //   switch (type) {
  // //     case UserType.User: {
  // //       let user = await new DbUser().fromEmail(email, {
  // //         disableCatchError: true,
  // //       });
  // //       if (user && !user.sso) {
  // //         throw ClientException.new({
  // //           overrideMessage: "Treedis user exists with this email address",
  // //           code: Code.ACCESS_DENIED_ERROR,
  // //         });
  // //       }
  // //       if (!user) {
  // //         user = new DbUser();
  // //         user.name = "SSO User";
  // //         user.email = email;
  // //         user.sso = true;
  // //         await user.save();
  // //       }
  // //       const treedisToken = createTokenJWT(user.id, user.type);

  // //       data = { user, token: treedisToken };
  // //       break;
  // //     }
  // //     case UserType.Client: {
  // //       const client = await new DbClient().fromEmail(email);
  // //       if (client && !client.sso) {
  // //         throw ClientException.new({
  // //           overrideMessage:
  // //             "SSO email address matches client address in Treedis database",
  // //           code: Code.ACCESS_DENIED_ERROR,
  // //         });
  // //       }
  // //       if (!client) {
  // //         // client.userId is required !!!
  // //         // No idea how detect userId on page ../admin/client/login
  // //         throw ClientException.new({
  // //           overrideMessage: "Not ready yet",
  // //           code: Code.BAD_REQUEST_ERROR,
  // //         });
  // //       }
  // //       const clientToken = createTokenJWT(client.id, Role.Client);

  // //       data = { user: client, token: clientToken };
  // //       break;
  // //     }
  // //     case UserType.Agent: {
  // //       const agent = await new DbAgent().fromEmail(email);
  // //       if (agent && !agent.sso) {
  // //         throw ClientException.new({
  // //           overrideMessage:
  // //             "SSO email address matches agent address in Treedis database",
  // //           code: Code.ACCESS_DENIED_ERROR,
  // //         });
  // //       }
  // //       if (!agent) {
  // //         // agent.clientId is required !!!
  // //         // No idea how detect clientId on page ../admin/agent/login
  // //         throw ClientException.new({
  // //           overrideMessage: "Not ready yet",
  // //           code: Code.BAD_REQUEST_ERROR,
  // //         });
  // //       }
  // //       const agentToken = createTokenJWT(agent.id, Role.Agent);
  // //       data = { user: agent, token: agentToken };
  // //       break;
  // //     }
  // //     default: {
  // //       break;
  // //     }
  // //   }
  // //   return data;
  // // }

  // @rateLimiter.decorator("email")
  // public async userLogin(
  //   email: string,
  //   password: string,
  //   origin: string = null
  // ) {
  //   if (!(email && password)) {
  //     throw ClientException.new({
  //       overrideMessage: "Missing email/password",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   const isUserExist = await new DbUser().checkExisting(email.toLowerCase());
  //   if (!isUserExist) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }

  //   const user = await new DbUser().getUserByEmail(
  //     email.toLowerCase(),
  //     password
  //   );

  //   if (!user) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }
  //   const matchedPass = passwordHash.verify(password, user.password);
  //   if (!matchedPass) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }

  //   this.isConfirmedUser(user);

  //   if (user.twoFA) {
  //     user.pinTwoFA = getRandomNumber(TWO_FA_RANDOM_NUMBER);
  //     this.queues[EnumQueue.Email].sendEmail(
  //       "sendAuthPin",
  //       config.get("mail.from"),
  //       user.email,
  //       {
  //         name: user.name,
  //         pin: user.pinTwoFA,
  //       }
  //     );
  //     await user.save();
  //     return { user, token: null };
  //   }
  //   return this.onSuccessAuth(user, origin);
  // }

  // @rateLimiter.decorator("email")
  // public async userLoginWithoutTwoFa(
  //   email: string,
  //   password: string,
  //   origin: string = null
  // ) {
  //   if (!(email && password)) {
  //     throw ClientException.new({
  //       overrideMessage: "Missing email/password",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }
  //   const user = await new DbUser().getUserByEmail(
  //     email.toLowerCase(),
  //     password
  //   );

  //   if (!user) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }
  //   const matchedPass = passwordHash.verify(password, user.password);
  //   if (!matchedPass) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }

  //   this.isConfirmedUser(user);

  //   return this.onSuccessAuth(user, origin);
  // }

  // @rateLimiter.decorator("email")
  // public async loginByPin(
  //   email: string,
  //   password: string,
  //   pin: string,
  //   origin: string = null
  // ) {
  //   const user = await new DbUser().getUserByEmail(email.toLowerCase());

  //   if (!user) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }
  //   const matchedPass = passwordHash.verify(password, user.password);
  //   if (!matchedPass) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }

  //   if (pin && user.pinTwoFA !== pin) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong pin",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   user.pinTwoFA = null;
  //   await user.save();
  //   return this.onSuccessAuth(user, origin);
  // }

  // // private async onSuccessAuth(user: User, origin: string) {
  // //   const token = createTokenJWT(user.id, user.type);

  // //   delete user.password;
  // //   delete user.emailToken;
  // //   delete user.passwordToken;
  // //   if (user.country) {
  // //     const timezones = ct.getTimezonesForCountry(user.country);
  // //     if (timezones && timezones.length > 0) {
  // //       user.timezone = timezones[0].utcOffset / MINUTES;
  // //     }
  // //   }
  // //   return { user, token };
  // // }

  // @rateLimiter.decorator("email")
  // public async clientLogin(email: string, password: string) {
  //   if (!(email && password)) {
  //     throw ClientException.new({
  //       overrideMessage: "Missing email/password",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   const isClientExist = await new DbClient().checkExisting(
  //     email.toLowerCase()
  //   );
  //   if (!isClientExist) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong password or email",
  //       code: Code.BAD_REQUEST_ERROR,
  //       metadata: { logLevel: LogLevel.INFO },
  //     });
  //   }

  //   const client = await new DbClient().fromEmailWithPermissions(
  //     email.toLowerCase()
  //   );

  //   if (client?.status === "disabled") {
  //     throw ClientException.new({
  //       overrideMessage:
  //         "Your account has been dissabled. Please contact your administrator",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   if (!client) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong email or password",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  //   const matchedPass = passwordHash.verify(password, client.password);
  //   if (!matchedPass) {
  //     throw ClientException.new({
  //       overrideMessage: "Wrong email or password",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  //   delete client.password;

  //   client.token = createTokenJWT(client.id, Role.Client);
  //   logger.info(`public/clientLogin: id ${client.id}`);
  //   return client;
  // }

  // // @rateLimiter.decorator("forgotPassword")
  // // public async forgotPassword(email) {
  // //   if (!email) {
  // //     throw ClientException.new({
  // //       overrideMessage: "Email is required",
  // //       code: Code.VALIDATION_ERROR,
  // //     });
  // //   }
  // //   const user = await new DbUser().fromEmail(email, {
  // //     disableCatchError: true,
  // //   });

  // //   if (!user) {
  // //     return true;
  // //   }

  // //   this.isConfirmedUser(user);
  // //   user.passwordToken = uuid();
  // //   await user.save();

  // //   this.queues[EnumQueue.Email].sendEmail(
  // //     "passwordReset",
  // //     config.get("mail.from"),
  // //     user.email,
  // //     {
  // //       name: user.name,
  // //       token: user.passwordToken,
  // //     }
  // //   );
  // //   logger.info(`public/forgotPassword: id ${user.id}`);
  // //   return true;
  // // }

  // // public async resetPassword(passwordToken, newPassword) {
  // //   try {
  // //     const user = await new DbUser().fromPasswordToken(passwordToken);
  // //     this.isConfirmedUser(user);

  // //     user.password = passwordHash.generate(newPassword);
  // //     user.passwordToken = null;
  // //     await user.save();

  // //     const token = createTokenJWT(user.id, user.type);
  // //     delete user.password;
  // //     delete user.emailToken;
  // //     delete user.passwordToken;

  // //     logger.info(`public/resetPassword: id ${user.id}`);
  // //     return { user, token };
  // //   } catch (error) {
  // //     if (error instanceof ClientException) throw error;
  // //     throw ClientException.new({
  // //       overrideMessage: error.message,
  // //       code: Code.BAD_REQUEST_ERROR,
  // //     });
  // //   }
  // // }

  // // public async confirmEmail(emailToken: string) {
  // //   try {
  // //     if (!emailToken) {
  // //       throw ClientException.new({
  // //         overrideMessage: "Email token is required",
  // //         code: Code.VALIDATION_ERROR,
  // //       });
  // //     }

  // //     const user = await new DbUser().fromEmailToken(emailToken);
  // //     user.emailToken = null;
  // //     user.confirmed = true;
  // //     if (user.tmpEmail) user.email = user.tmpEmail;
  // //     user.tmpEmail = null;
  // //     if (user.newsletterEnabled) {
  // //       await ((await alreadyRegistered(user.email))
  // //         ? subscribeMailchimp(user.email, user.name)
  // //         : addMailchimp(user.email, user.name));
  // //     }

  // //     if (!user.stripeId) {
  // //       user.stripeId = await this.stripe.createCustomer({
  // //         name: user.name,
  // //         email: user.email,
  // //         phone: user.phone,
  // //       });
  // //     }

  // //     await user.save();
  // //     const token = createTokenJWT(user.id, user.type);
  // //     delete user.password;
  // //     delete user.emailToken;
  // //     delete user.passwordToken;

  // //     logger.info(`public/confirmEmail: id ${user.id}`);
  // //     return { user, token };
  // //   } catch (error) {
  // //     if (error instanceof ClientException) throw error;
  // //     throw ClientException.new({
  // //       overrideMessage: error.message,
  // //       code: Code.BAD_REQUEST_ERROR,
  // //     });
  // //   }
  // // }

  // private isConfirmedUser(user) {
  //   if (!user.confirmed) {
  //     throw ClientException.new({
  //       overrideMessage: "E-Mail is not confirmed",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  //   if (!user.active) {
  //     throw ClientException.new({
  //       overrideMessage: "User is deactivated",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  public async confirmEmail(data, data2) {}
  public async forgotPassword(data) {}
  public async userLogin(data) {}
}

export default AuthController;

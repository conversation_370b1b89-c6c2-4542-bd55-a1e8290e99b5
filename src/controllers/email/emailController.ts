import EmailTemplateController from './emailTemplateController';

class EmailController {
  client: any;

  queue: any;

  emailTemplateController: EmailTemplateController;

  constructor(client, queue = null) {
    this.queue = queue;
    this.client = client;
    this.emailTemplateController = new EmailTemplateController(client);
  }

  async sendEmail(
    {
      userId,
      to,
      from = process.env.TREEDIS_EMAIL,
      emailType,
      variables,
    }: {
      userId: number;
      to: string;
      emailType: string;
      variables: any;
      from?: string;
    },
    throwExceptions = false,
  ): Promise<void> {
    try {
      const [template, subject] =
        await this.emailTemplateController.getTemplateByName(userId, emailType);
      if (template) {
        await this.queue.sendEmail(emailType, from, to, {
          template,
          subject,
          variables,
        });
      }
    } catch (error) {
      if (throwExceptions) throw error;
    }
    return;
  }
}

export default EmailController;

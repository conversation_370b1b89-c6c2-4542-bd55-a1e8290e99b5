// import { DbEmailTemplate, DbEmailTranslation } from '@treedis/db-access';

import { Code } from "../../core/common/code/Code";
import { ClientException } from "../../core/common/exception/ClientException";
import { logger } from "../../log";

class EmailTemplateController {
  client: any;

  constructor(apiClient) {
    this.client = apiClient;
  }

  // async getAll(id: number) {
  //   try {
  //     const translations = await new DbEmailTemplate().getAllWithUser(id);

  //     const translationsWithTemplates = translations.map(
  //       async (translation) => {
  //         if (!translation?.emailTranslation?.text) {
  //           const templateResponse = await this.client.getTemplate(
  //             translation.templateId
  //           );
  //           translation.text = templateResponse.HtmlBody;
  //         }
  //         return translation;
  //       }
  //     );
  //     return await Promise.all(translationsWithTemplates);
  //   } catch (error) {
  //     logger.error(
  //       `EmailTemplateController>>getAll. Message: ${error.message}`
  //     );
  //     throw ClientException.new({
  //       overrideMessage: "Error get all templates",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }

  // async getTemplateByName(userId: number, templateAlias: string) {
  //   try {
  //     let emailTemplate = await new DbEmailTemplate().fromName(
  //       userId,
  //       templateAlias
  //     );
  //     let template = emailTemplate?.emailTranslation?.text;
  //     let subject = emailTemplate?.title;
  //     if (!template && emailTemplate) {
  //       emailTemplate = await this.client.getTemplate(emailTemplate.templateId);
  //       template = emailTemplate.HtmlBody;
  //       subject = emailTemplate.Subject;
  //     }
  //     return [template, subject];
  //   } catch (error) {
  //     logger.error(
  //       `Error get templates for send email. Message: ${error.message}`
  //     );
  //   }
  //   return null;
  // }

  // bulkSave(id, translations) {
  //   try {
  //     return new DbEmailTranslation().bulkSave(id, translations);
  //   } catch (error) {
  //     logger.error(
  //       `EmailTemplateController>>bulkSave. Message: ${error.message}`
  //     );
  //     throw ClientException.new({
  //       overrideMessage: "Error save translations",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }
  // }
}

export default EmailTemplateController;

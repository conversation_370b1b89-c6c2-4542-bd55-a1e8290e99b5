// import {
//   CreateAnalyticEventType,
//   CreateAnalyticUserType,
//   DbAnalyticEvent,
//   DbAnalyticUser,
// } from '@treedis/db-access';
import config from "src/config";
import {
  AnalyticEventRequestDto,
  AnalyticUserRequestDto,
} from "src/controllers/analytic/dto";
import { logger } from "src/log";
import AnalyticQueue from "src/queues/analytic.queue";
import { uuid } from "uuidv4";

export enum SourceEnum {
  DIRECT = "Direct",
  REFERRAL = "Referral",
  EMAIL = "Email",
}

class AnalyticController {
  analyticQueue: AnalyticQueue;

  constructor(analyticQueue) {
    this.analyticQueue = analyticQueue;
  }

  // public async createGameEvent(
  //   data: CreateAnalyticEventType,
  //   documentReferer: string
  // ) {
  //   const {
  //     type,
  //     fingerprint,
  //     documentLocation,
  //     documentTitle,
  //     category,
  //     tourId,
  //     tourInfo,
  //     action,
  //     gameAnalytic,
  //     eventType,
  //   } = data;
  //   if (!tourId && !tourInfo?.tourId) {
  //     logger.error(JSON.stringify(data));
  //     throw new Error("Tour id is required");
  //   }
  //   const formatedData: Partial<CreateAnalyticEventType> = {
  //     uuid: gameAnalytic.id || uuid(),
  //     type: eventType || type,
  //     fingerprint,
  //     documentTitle,
  //     category,
  //     documentReferer,
  //     action,
  //     data: { ...gameAnalytic },
  //     createdAt: Date.now(),
  //     documentLocation: documentLocation?.href || "",
  //     tourId: tourId || tourInfo?.tourId,
  //   };
  //   return this.analyticQueue.saveGameAnalytic(formatedData);
  // }

  // private eventMapper(data: CreateAnalyticEventType, documentReferer: string) {
  //   const {
  //     type,
  //     fingerprint,
  //     documentLocation,
  //     documentTitle,
  //     category,
  //     tourId,
  //     browser,
  //     tour,
  //     arPath,
  //     label,
  //     time,
  //     quantity,
  //     action,
  //     eventId,
  //     device,
  //     eventType,
  //     gameAnalytic,
  //     tourInfo,
  //   } = data;

  //   if (
  //     category.toLowerCase().startsWith("ar") ||
  //     action.toLowerCase().startsWith("ar")
  //   ) {
  //     logger.info(`EVENT FORM AR ${category} ${action}`);
  //   }
  //   if (!tourId && !tourInfo?.tourId) {
  //     logger.error(JSON.stringify(data));
  //     throw new Error("Tour id is required");
  //   }

  //   const appUrl = config.get("general.appDomain");

  //   const formattedData: Partial<CreateAnalyticEventType> = {
  //     uuid: uuid(),
  //     type: eventType || type,
  //     fingerprint,
  //     documentTitle,
  //     category,
  //     label,
  //     documentReferer: "",
  //     data: { device, eventId },
  //     action,
  //     createdAt: Date.now(),
  //     documentLocation: documentLocation?.href || "",
  //     tourId,
  //   };

  //   if (time) {
  //     formattedData.data = {
  //       ...formattedData.data,
  //       ...time,
  //       totalTime: time.endTime - time.startTime,
  //     };
  //   }
  //   if (browser) formattedData.data.browser = browser;
  //   if (quantity) formattedData.data.quantity = quantity;
  //   if (gameAnalytic)
  //     formattedData.data = { ...formattedData.data, ...gameAnalytic };
  //   if (arPath)
  //     formattedData.data = {
  //       ...formattedData.data,
  //       ...arPath,
  //       total: arPath.endTime - arPath.startTime,
  //     };

  //   formattedData.data.source =
  //     !documentReferer || documentReferer.includes(appUrl)
  //       ? SourceEnum.DIRECT
  //       : documentReferer.includes("mail.google.com")
  //       ? SourceEnum.EMAIL
  //       : SourceEnum.REFERRAL;

  //   return formattedData;
  // }

  // public async createEvent(
  //   data: CreateAnalyticEventType,
  //   documentReferer: string
  // ) {
  //   const formatedData = this.eventMapper(data, documentReferer);
  //   return this.analyticQueue
  //     .createAnalyticRecord(formatedData)
  //     .catch((error) => {
  //       logger.error(`error creating analytic event ${JSON.stringify(error)}`);
  //     });
  // }

  // public async createUser(data: CreateAnalyticUserType) {
  //   return await new DbAnalyticUser().createOrGet(data);
  // }

  // public async getEvent(tourId: number, filters: string) {
  //   const filterOptions = JSON.parse(filters);
  //   const mapFilter = AnalyticEventRequestDto.mapFrom(filterOptions);
  //   return await new DbAnalyticEvent().getByDate(tourId, mapFilter);
  // }

  // public async getUser(filters: any) {
  //   const filterOptions = JSON.parse(filters);
  //   const mapFilter = AnalyticUserRequestDto.mapFrom(filterOptions);
  //   return await new DbAnalyticUser().get(mapFilter);
  // }
}

export default AnalyticController;

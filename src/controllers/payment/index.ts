// import { DbECommerceSettings, DbTicketingGeneral } from '@treedis/db-access';
import { ClientException, Code } from "src/core/common";
import StripePayment from "src/core/service/stripe";

class PaymentController {
  private client: StripePayment;

  // TODO:: refactor
  // async init(tourId: number, type: string) {
  //   let settings;
  //   let apiKey;
  //   if (type === "tickets") {
  //     settings = await new DbTicketingGeneral().get({
  //       query: {
  //         tourId,
  //       },
  //       disableCatchError: true,
  //     });
  //     apiKey = settings.apiUsername || settings.apiSecretKey;
  //   } else {
  //     settings = await new DbECommerceSettings().get({
  //       query: {
  //         tourId,
  //       },
  //       includeAll: true,
  //       disableCatchError: true,
  //     });
  //     apiKey = settings.stripeServerApiKey;
  //   }

  //   if (!apiKey) {
  //     throw ClientException.new({
  //       code: Code.BAD_REQUEST_ERROR,
  //       overrideMessage: "Stripe payment method not configured",
  //     });
  //   }
  //   this.client = new StripePayment({
  //     apiKey,
  //   });
  // }

  // async createClientSecret({ tourId, type, ...data }) {
  //   await this.init(tourId, type);
  //   console.log("before pay", data);
  //   // @ts-ignore
  //   return this.client.pay(data);
  // }
}

export default PaymentController;

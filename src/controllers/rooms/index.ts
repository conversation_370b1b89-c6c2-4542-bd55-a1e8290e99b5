import { DbRoomCategory } from "src/db/lib/property/roomCategory";

class RoomsController {
  queues;

  constructor(queues) {
    this.queues = queues;
  }

  public async getCategoriesWithRooms() {
    try {
      const categories = await new DbRoomCategory().getRoomCategoriesWithRooms(
        1
      );
      console.log("categories in controller!!!!", categories);
      return categories;
    } catch (error) {
      return null;
    }
  }
}

export default RoomsController;

import * as cls from 'cls-hooked';
import { uuid } from 'uuidv4';

const store = cls.createNamespace(`correlation-id-namespace`);

const CORRELATION_ID_KEY = `correlation-id`;

export function withId(function_: () => void, id: string) {
  store.run(() => {
    store.set(CORRELATION_ID_KEY, id || uuid());
    function_();
  });
}

export function getId() {
  return store.get(CORRELATION_ID_KEY);
}
export const bind = store.bind.bind(store);

export const bindEmitter = store.bindEmitter.bind(store);

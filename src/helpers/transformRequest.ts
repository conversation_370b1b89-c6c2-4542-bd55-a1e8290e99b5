export function transformToFloat(value: any): any {
  if (value == undefined || value === 'null') {
    return value === 'null' ? null : value;
  }
  return Number.parseFloat(value);
}

export function transformToNumber(value: any): any {
  return value === 'null' || value === undefined ? undefined : Number(value);
}

export function transformToCoordinates(value: any): any {
  if (value === undefined || value === null || value === '') return undefined;
  const [lat, lng] = value.split(',');
  return { lat: Number.parseFloat(lat), lng: Number.parseFloat(lng) };
}

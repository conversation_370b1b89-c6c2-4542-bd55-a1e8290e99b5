const groupBy = <T>(items: T[], group: string) =>
  items.reduce((accumulator, item) => {
    accumulator[item[group]] = [...(accumulator[item[group]] || []), item];
    return accumulator;
  }, {});

const toDictionary = <T>(list: T[], userDefinedOptions) => {
  const defaultOptions = {
    indexKey: 'name',
    valueKey: 'value',
  };

  if (!Array.isArray(list)) {
    throw new TypeError('Cannot convert a non-array value to dictionary.');
  }

  const { indexKey, valueKey, transformValues } = {
    ...defaultOptions,
    ...userDefinedOptions,
  };
  return list.reduce((dictionary, item) => {
    if (!item || !(item[indexKey] || item[indexKey] === 0)) {
      return dictionary;
    }
    return {
      ...dictionary,
      [item[indexKey]]: transformValues
        ? transformValues(item)
        : item[valueKey],
    };
  }, {});
};

const uniqueByKey = (array: any[], key: string) => [
  ...new Map(array.map((item) => [item[key], item])).values(),
];

const swap = (array: Array<any>, index1: number, index2: number) =>
  array.map((item, id, array_) => {
    if (id === index1) return array_[index2];
    if (id === index2) return array_[index1];
    return item;
  });

const array = {
  groupBy,
  toDictionary,
  uniqueByKey,
  swap,
};

export default array;

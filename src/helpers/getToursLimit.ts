const TOURS_LIMIT = {
  Free: 5,
  Growth: 50,
  // Business and Enterprise are unlimited
  Unlimited: 999_999,
};

enum SubscriptionType {
  GrowthMonth = 'growth_month',
  GrowthYear = 'growth_year',
  Growth = 'growth',
  BusinessMonth = 'business_month',
  Business = 'business',
  BusinessYear = 'business_year',
  Free = 'free',
  Enterprise = 'enterprise',
}

export function getToursLimit(plan: SubscriptionType, userId?: number) {
  let toursLimit = TOURS_LIMIT.Free;

  switch (plan) {
    case SubscriptionType.Growth:
    case SubscriptionType.GrowthMonth:
    case SubscriptionType.GrowthYear: {
      toursLimit = TOURS_LIMIT.Growth;
      break;
    }
    case SubscriptionType.Business:
    case SubscriptionType.BusinessMonth:
    case SubscriptionType.BusinessYear:
    case SubscriptionType.Enterprise: {
      toursLimit = TOURS_LIMIT.Unlimited;
      break;
    }
    default: {
      break;
    }
  }

  // Unlimited tours for userId 1
  if (userId === 1) {
    toursLimit = TOURS_LIMIT.Unlimited;
  }

  return toursLimit;
}

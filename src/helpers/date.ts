import { isNumber } from 'class-validator';
import * as moment from 'moment';
import { requestHelper } from 'src/helpers/index';

export const DB_DATE_FORMAT = 'YYYY-MM-DD';

export const DATE_FORMAT = 'DD/MM/YYYY';

export const TIME_FORMAT = 'HH:mm:ss';

export const ONE_SECOND = 1000;

export const ONE_MINUTE = 60;

export const ONE_HOUR = 60;

export const ONE_DAY = 24 * ONE_HOUR * ONE_MINUTE * ONE_SECOND;

export const ONE_MONTH = 30 * ONE_DAY;

export const toMomentTime = (time: string): moment.Moment =>
  moment(time, TIME_FORMAT);

export const toDateTimeFormat = (data: string | Date): string =>
  moment(data).format(`${DATE_FORMAT} ${TIME_FORMAT}`);

export const isTimeBetween = (
  time: string,
  startTime: string,
  endTime: string,
) =>
  toMomentTime(time).isBetween(toMomentTime(startTime), toMomentTime(endTime));

export const isTimeSameOrBetween = (
  time: string,
  startTime: string,
  endTime: string,
): boolean =>
  isTimeBetween(time, startTime, endTime) ||
  toMomentTime(time).isSame(toMomentTime(startTime)) ||
  toMomentTime(time).isSame(toMomentTime(endTime));

export const timeToTZ = (time: string, tz: number, format = TIME_FORMAT) =>
  moment(time, 'HH:mm')
    .utcOffset(tz * ONE_HOUR)
    .format(format);

export const addMinuteToTime = (time: string, minutes: number): string =>
  toMomentTime(time).add(minutes, 'minutes').format(TIME_FORMAT);

export const getDiffHours = (startTime: string, endTime: string): number =>
  moment
    .duration(moment(startTime, TIME_FORMAT).diff(moment(endTime, TIME_FORMAT)))
    .asHours();

export const createTimeSlots = (
  timeInterval: number,
  startScanTime: string,
  endScanTime: string,
) => {
  const dayTimeSlots = [];
  let startTime = toMomentTime(startScanTime).format(TIME_FORMAT);
  let index = 0;

  // TODO:: add round time to 30 min
  while (startTime !== endScanTime) {
    const endTime = toMomentTime(startTime)
      .add(timeInterval, 'minutes')
      .format(TIME_FORMAT);
    dayTimeSlots.push({
      start: startTime,
      end: endTime,
    });
    startTime = endTime;
    index++;
  }
  return dayTimeSlots;
};

export const getTimezoneByLocation = async (lat: number, lng: number) => {
  try {
    if (!lat || !isNumber(lat) || lng || !isNumber(lng)) {
      new Error('Timezone requires valid latitude and longitude coordinates.');
    }
    const timestamp = Math.round(Date.now() / 1000);
    const location = `${lat},${lng}`;
    const { data: result }: any = await requestHelper.fetch<{
      rawOffset: number;
    }>(
      `https://maps.googleapis.com/maps/api/timezone/json?location=${location}&timestamp=${timestamp}&key=${process.env.GOOGLE_MAPS_API_KEY}`,
      {
        method: 'GET',
      },
    );
    return result.rawOffset;
  } catch (error) {
    console.error(error);
  }
  return null;
};

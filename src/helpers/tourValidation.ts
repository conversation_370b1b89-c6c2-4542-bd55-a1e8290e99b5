export function tourValidation(tour, requestBody) {
  if (!tour) {
    return {
      hasError: true,
      error: { status: 404, message: 'No tour with that ID found' },
    };
  }

  if (!tour.user?.active) {
    return {
      hasError: true,
      error: { status: 404, message: 'User not active' },
    };
  }

  if (requestBody.private && tour.userId != requestBody.userId) {
    return { hasError: true, error: { status: 403, message: 'Not your tour' } };
  }
  return { hasError: false };
}

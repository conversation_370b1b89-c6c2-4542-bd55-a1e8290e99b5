import { requestHelper } from 'src/helpers/index';

export const tourLogin = async (
  matterId: string,
  password: string,
): Promise<string | null> => {
  if (!password) return null;
  const {
    data: { token },
  } = await requestHelper.fetch<{ token: string }>(
    `https://my.matterport.com/api/v2/models/${matterId}/public-access`,
    { method: 'POST', data: { password } },
  );
  return token || null;
};

export class MatterportAPI {
  private token: string | null = null;

  private readonly modelId: string;

  private readonly headers: Record<string, string> = {};

  constructor(modelId: string) {
    if (!modelId) throw new Error('Wrong matterport id');
    this.modelId = modelId;
  }

  public async getFiles() {
    const { data: files } = await requestHelper.fetch(
      `https://my.matterport.com/api/player/models/${this.modelId}/files?type=1`,
      {
        method: 'GET',
        headers: this.headers,
      },
    );
    return files;
  }

  public async getSpecification() {
    const files = await this.getFiles();
    const specificationUrl = files['render/vr_colorplan.json'];
    let specification = null;
    if (specificationUrl) {
      const { data } = await requestHelper.fetch(specificationUrl, {
        method: 'GET',
        headers: this.headers,
      });
      specification = data;
    }

    return specification;
  }

  public async login(matterId: string, password: string): Promise<void> {
    if (password) {
      const {
        data: { token },
      } = await requestHelper.fetch<{ token: string }>(
        `https://my.matterport.com/api/v2/models/${matterId}/public-access`,
        {
          method: 'POST',
          data: { password },
        },
      );
      this.token = token || null;
      if (this.token) {
        this.headers.Authorization = `Matterport-Object-Access ${this.token}`;
      }
    }
  }
}

import * as fs from "node:fs";
import * as path from "node:path";

import { Request, Response, Router } from "express";
import * as moment from "moment";
import config from "src/config";
import { STRIPE_PLAN_IDS } from "src/constants";
// import FolderController from "src/controllers/folder";
// import TourController from "src/controllers/tour";
import UserController from "src/controllers/user";
import {
  ClientException,
  Code,
  EnumQueue,
  handleError,
  RequestType,
  Role,
} from "src/core/common";
import StripePayment from "src/core/service/stripe";
// import authorize from "src/lib/permission";
import { logger } from "src/log";
import { BotQueue, EmailQueue } from "src/queues";
import { uuid } from "uuidv4";

// import { Subscription } from "./types";
// import {
//   cancelPaypalSubscription,
//   createPaypalPayout,
//   getCurrency,
// } from "./util";

export enum TourPlan {
  Free = "free",
  Growth = "growth",
  Business = "business",
  Enterprise = "enterprise",
}

// const PLANS = new Set([
//   Subscription.Growth,
//   Subscription.GrowthMonth,
//   Subscription.GrowthYear,
//   Subscription.Business,
//   Subscription.BusinessMonth,
//   Subscription.BusinessYear,
//   Subscription.Free,
//   Subscription.Enterprise,
// ]);

const CLIENTS_LIMIT = {
  Free: 1,
  Growth: 25,
  Business: 100,
  Enterprise: 100,
  Unlimited: 999_999,
};

const TOURS_LIMIT = {
  Free: 5,
  Growth: 50,
  // Business and Enterprise are unlimited
  Unlimited: 999_999,
};

class BillingRouter {
  router: Router;

  stripe: StripePayment;

  // controller: TourController;

  // folderController: FolderController;

  botQueue: BotQueue;

  constructor() {
    this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    this.botQueue = new BotQueue();
    // this.controller = new TourController({
    //   [EnumQueue.AR]: new ARQueue(),
    //   [EnumQueue.Bot]: this.botQueue,
    // });
    // this.folderController = new FolderController();
    this.router = Router();
    this.init();
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/getCard")
  public async getCard(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    // if (!user.stripeId)
    //   return res.status(422).json({ message: "No Stripe ID" });
    // const card = await this.stripe.getCard(user.stripeId);
    return res.status(200).json({ message: "Success", card: "" });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/deleteCard")
  public async deleteCard(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    // await this.stripe.deleteCard(user.stripeId);
    return res.status(200).json({ message: "Success" });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/getInvoice")
  public async getInvoice(request: RequestType<Request>, res: Response) {
    // const invoice = await this.stripe.getInvoice(request.params.id);
    return res.status(200).json({ message: "Success", invoice: "" });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/listInvoices")
  public async listInvoices(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    // const invoices = await this.stripe.getInvoices(
    //   user.stripeId,
    //   request.body.start,
    //   request.body.end,
    //   request.body.limit,
    //   request.body.starting_after
    // );
    return res.status(200).json({ message: "Success", invoices: [] });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/session")
  public async getSession(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    const clientSecret = ""; //await this.stripe.createIntent(user.stripeId);
    return res
      .status(200)
      .json({ message: "Success", client_secret: clientSecret });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/getCustomer")
  public async getCustomer(request: RequestType<Request>, res: Response) {
    const user = { stripeId: "" };
    // await new DbUser().fromId(request.account.id);
    // if (!user.stripeId) {
    //   user.stripeId = await this.stripe.createCustomer({
    //     name: user.name,
    //     email: user.email,
    //     phone: user.phone,
    //   });
    //   await user.save();
    // }
    return res
      .status(200)
      .json({ message: "Success", stripeId: user.stripeId });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/pay")
  public async pay(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    // const card = await this.stripe.getCard(user.stripeId);
    // if (!card) {
    //   throw ClientException.new({
    //     overrideMessage: "No card on file",
    //     code: Code.BAD_REQUEST_ERROR,
    //   });
    // }

    // const payment = await this.stripe.pay({
    //   amount: request.body.price,
    //   currency: "USD",
    //   confirm: true,
    //   off_session: true,
    //   customer: user.stripeId,
    //   payment_method: card.id,
    //   description: request.body.description,
    // });

    return res.status(200).json({ message: "Success", payment: "" });
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/payCustom")
  public async payCustom(request: RequestType<Request>, res: Response) {
    // const user = await new DbUser().fromId(request.account.id);
    // const card = await this.stripe.getCard(user.stripeId);
    // if (!card) {
    //   throw ClientException.new({
    //     overrideMessage: "No card on file",
    //     code: Code.BAD_REQUEST_ERROR,
    //   });
    // }

    // const { amount, currency, description } = request.body;

    // await this.stripe.pay({
    //   amount,
    //   currency,
    //   confirm: true,
    //   off_session: true,
    //   customer: user.stripeId,
    //   payment_method: card.id,
    //   description,
    // });

    //TODO: send invoice in email using email queue
    return res.status(200).json({ message: "Success" });
  }

  // private async handleOldSubscription(chosenSub: Subscription, user: DbUser) {
  //   //support old users switching
  //   if (
  //     PLANS.has(chosenSub) &&
  //     user.subscriptionId?.includes("I-") &&
  //     (user.subscription === "year" || user.subscription === "month")
  //   ) {
  //     //refund
  //     let amount = 0;
  //     if (user.subscription === "year") {
  //       amount =
  //         (499 / 365) *
  //         moment(user.subscriptionStart).add(1, "year").diff(moment(), "days");
  //     }
  //     if (user.subscription === "month") {
  //       amount =
  //         (49 / moment().daysInMonth()) *
  //         moment(user.subscriptionStart).add(1, "month").diff(moment(), "days");
  //     }
  //     createPaypalPayout(
  //       amount.toFixed(2),
  //       user.email,
  //       getCurrency(user.country).ISO
  //     ).catch((error: any) => {
  //       logger.error("Payout error: " + error.message);
  //     });
  //     cancelPaypalSubscription(user.subscriptionId).catch((error: any) => {
  //       logger.error("Canceling paypal sub error: " + error.message);
  //     }); //we are not awaiting because it might fail
  //   } else if (
  //     user.subscriptionId &&
  //     PLANS.has(chosenSub) &&
  //     user.subscriptionId.includes("sub_")
  //   ) {
  //     //before upgrading or downgrading, cancel old subscription

  //     //TODO: refund the difference

  //     //We remove subscription to prevent the webhook deleting the user tours
  //     const subId = user.subscriptionId;
  //     user.subscriptionId = null;

  //     await user.save();
  //     await this.stripe.deleteSubscription(subId);
  //   }
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError("billing/updateSubscription")
  public async updateSubscription(
    request: RequestType<Request>,
    res: Response
  ) {
    // const user = await new DbUser().fromId(request.account.id);
    // if (user.subscriptionId !== request.body.subscriptionId) {
    //   throw ClientException.new({
    //     overrideMessage: "Unathorized",
    //     code: Code.BAD_REQUEST_ERROR,
    //   });
    // }
    // if (!request.body.subscriptionId.includes("free_")) {
    //   await this.stripe.updateSubscription(
    //     request.body.subscriptionId,
    //     request.body.params
    //   );
    // }
    return res.status(200).json({ message: "Success" });
  }

  // Users which purchased wl or which are one business or enterprise, should not pay for mini wl
  // private async hasFreeMiniWl(subscription: string, user: DbUser) {
  //   return (
  //     ["mini_whitelabel", "mini_full_whitelabel"].includes(subscription) &&
  //     (!!user?.whitelabelId ||
  //       user?.subscription === "business" ||
  //       user?.subscription === "enterprise" ||
  //       user?.subscription === "year" ||
  //       !!user?.enterprise)
  //   );
  // }

  // private getToursLimit(plan: Subscription, userId?: number) {
  //   let toursLimit = TOURS_LIMIT.Free;

  //   switch (plan) {
  //     case Subscription.Growth:
  //     case Subscription.GrowthMonth:
  //     case Subscription.GrowthYear: {
  //       toursLimit = TOURS_LIMIT.Growth;
  //       break;
  //     }
  //     case Subscription.Business:
  //     case Subscription.BusinessMonth:
  //     case Subscription.BusinessYear:
  //     case Subscription.Enterprise: {
  //       toursLimit = TOURS_LIMIT.Unlimited;
  //       break;
  //     }
  //     default: {
  //       break;
  //     }
  //   }

  //   // Unlimited tours for userId 1
  //   if (userId === 1) {
  //     toursLimit = TOURS_LIMIT.Unlimited;
  //   }

  //   return toursLimit;
  // }

  // private lockPlanFeatures = async (
  //   plan: Subscription,
  //   userId: number,
  //   toursLimit: number
  // ) => {
  //   // Sometimes plan may be 'whitelabel', for example and in such cases we don't want to lock / unlock anything
  //   if (
  //     ![
  //       Subscription.Growth,
  //       Subscription.GrowthMonth,
  //       Subscription.GrowthYear,
  //       Subscription.Business,
  //       Subscription.BusinessMonth,
  //       Subscription.BusinessYear,
  //       Subscription.Enterprise,
  //       Subscription.Free,
  //     ].includes(plan)
  //   ) {
  //     return;
  //   }

  //   let clientsLimit = CLIENTS_LIMIT.Free;
  //   let tourPlan: TourPlan = TourPlan.Free;

  //   switch (plan) {
  //     case Subscription.GrowthMonth:
  //     case Subscription.GrowthYear: {
  //       clientsLimit = CLIENTS_LIMIT.Growth;
  //       toursLimit = this.getToursLimit(plan, userId);
  //       tourPlan = TourPlan.Growth;
  //       break;
  //     }
  //     case Subscription.BusinessMonth:
  //     case Subscription.BusinessYear:
  //     case Subscription.Enterprise: {
  //       clientsLimit = CLIENTS_LIMIT.Business;
  //       toursLimit = this.getToursLimit(plan, userId);
  //       tourPlan = TourPlan.Business;
  //       break;
  //     }
  //     default: {
  //       break;
  //     }
  //   }
  //   if (userId === 1) {
  //     // Unlimited <NAME_EMAIL>
  //     clientsLimit = CLIENTS_LIMIT.Unlimited;
  //     tourPlan = TourPlan.Enterprise;
  //   }

  //   // Disable clients
  //   const clients = await new DbClient().getAll(userId);
  //   const updatedClientPromises = clients?.rows?.map(async (c, index) => {
  //     const client = await new DbClient().fromId(c.dataValues.id);
  //     client.status = clientsLimit < index + 1 ? "disabled" : "enabled";
  //     await client.save(client.userId);
  //   });
  //   await Promise.all(updatedClientPromises);

  //   // Disable tours
  //   const tours = await new DbTour().getAll(
  //     userId,
  //     undefined,
  //     999_999, // to avoid default pagination,
  //     undefined,
  //     undefined,
  //     true // include duplicates
  //   );
  //   const flattenTours = tours?.rows.reduce((accumulator, t) => {
  //     accumulator.push(t);
  //     if (t.dataValues.duplicates.length > 0) {
  //       accumulator.push(...t.dataValues.duplicates);
  //     }
  //     return accumulator;
  //   }, []);

  //   const flattenTourPromises = flattenTours?.map(async (tour, index) => {
  //     if (toursLimit < index + 1) {
  //       tour.status = TourStatus.Disabled;
  //       tour.plan = TourPlan.Free;
  //     } else {
  //       tour.status = TourStatus.Ready;
  //       tour.plan = tourPlan;
  //     }
  //     await tour.save();
  //   });
  //   await Promise.all(flattenTourPromises);
  // };

  // public handleWhitelabel = async (
  //   user: DbUser,
  //   sub: any,
  //   request: RequestType<Request>
  // ) => {
  //   user.whitelabelId = sub.id;
  //   // sample tours logic
  //   let folder = await this.folderController.get(
  //     null,
  //     user.id,
  //     "Sample spaces"
  //   );

  //   if (folder.length === 0) {
  //     folder = await this.folderController.createOrUpdate(user.id, {
  //       label: "Sample spaces",
  //     });
  //   }
  //   const sampleTourNames = [
  //     "7d6a7d30.json",
  //     "hannovermesse2023.json",
  //     "icl-green-house.json",
  //     "icl-museum.json",
  //     "icl-outdoor-hazard.json",
  //     "mekorot-hebrew.json",
  //     "metaverse-store.json",
  //     "winery.json",
  //   ];
  //   for (const tourName of sampleTourNames) {
  //     const filepath = path.join(
  //       __dirname,
  //       "..",
  //       "..",
  //       "static",
  //       "samples",
  //       tourName
  //     );

  //     fs.readFile(filepath, async (error, data) => {
  //       if (error) {
  //         logger.info("no file found");
  //         return;
  //       }

  //       const result = JSON.parse(data.toString());
  //       //@ts-ignore
  //       const folderId = folder?.[0]?.id || folder?.id;

  //       await this.controller.import(
  //         {
  //           userId: user.id,
  //           object: {
  //             ...result,
  //             folderId,
  //             isSample: true,
  //           },
  //         },
  //         request.io
  //       );
  //     });
  //   }
  // };

  // CREATE SUBSCRIPTION LOGIC
  // private async sendEmail(emailKey: string, user: DbUser) {
  //   new EmailQueue().sendEmail(emailKey, config.get("mail.from"), user.email, {
  //     name: user.name,
  //   });
  // }

  // public async createSubscriptionLogic({
  //   userId,
  //   chosenSub,
  //   wlBundle,
  //   isProd,
  //   coupon,
  //   clientId,
  //   tourId,
  //   sendEmail,
  //   req,
  // }: {
  //   userId: number;
  //   chosenSub: Subscription;
  //   wlBundle: boolean;
  //   isProd: boolean;
  //   coupon?: string;
  //   clientId?: string;
  //   tourId?: string;
  //   sendEmail?: boolean;
  //   req: RequestType<Request>;
  // }) {
  //   const user = await new DbUser().fromId(userId);
  //   const oldUser = await new DbUser().fromId(userId);
  //   const [trialDuration, trialUnit]: [
  //     number,
  //     moment.unitOfTime.DurationConstructor
  //   ] = isProd ? [14, "days"] : [30, "minutes"];
  //   const { trialActivated, subscriptionId } = user;

  //   const isStripe = subscriptionId?.includes("sub_");

  //   const currentSubscription = isStripe
  //     ? await this.stripe.getSubscription(subscriptionId)
  //     : null;

  //   // @ts-ignore
  //   const planId = currentSubscription?.plan?.id;
  //   // If we already have a subscription and it's the same plan, we throw an error to avoid double billing
  //   // This ia a temporary safeguard until we find the root cause of the 'multiple billing' issue
  //   // For whitelabel, we check if the user already has a whitelabelId
  //   // Currently checking for business_month, business_year, growth_month, growth_year, whitelabel
  //   if (
  //     (planId && planId === STRIPE_PLAN_IDS[chosenSub]) ||
  //     (chosenSub === Subscription.Whitelabel && user.whitelabelId)
  //   ) {
  //     logger.info(
  //       `DOUBLE BILLING: User ${user.email} is already subscribed to ${chosenSub} with id: ${subscriptionId}`
  //     );

  //     throw ClientException.new({
  //       overrideMessage: "You are already subscribed to this plan",
  //       code: Code.BAD_REQUEST_ERROR,
  //     });
  //   }

  //   const isTrialing = currentSubscription?.status === "trialing";
  //   const trial_end =
  //     currentSubscription?.trial_end ||
  //     moment().add(trialDuration, trialUnit).unix();

  //   // wlBundle can be used only with business year
  //   if (chosenSub === Subscription.BusinessYear && wlBundle) {
  //     user.wlBundle = wlBundle;
  //   }

  //   if (chosenSub !== Subscription.Free) {
  //     const trialOn =
  //       PLANS.has(chosenSub) &&
  //       (isTrialing ||
  //         (!trialActivated &&
  //           user.subscription !== "year" &&
  //           user.subscription !== "month"));

  //     const card = await this.stripe.getCard(user.stripeId);
  //     const shouldNotPay = await this.hasFreeMiniWl(chosenSub, user);

  //     // If user shouldn't pay (for mini wl), we don't require a card
  //     if (!card && !shouldNotPay) {
  //       throw ClientException.new({
  //         overrideMessage: "No card on file",
  //         code: Code.BAD_REQUEST_ERROR,
  //       });
  //     }

  //     // If trialActivated is not activated yet, send welcome emails for growth and business
  //     if (
  //       !trialActivated &&
  //       (chosenSub.includes("growth") || chosenSub.includes("business"))
  //     ) {
  //       const emailKey =
  //         chosenSub === Subscription.GrowthMonth ||
  //         chosenSub === Subscription.GrowthYear
  //           ? "welcomeGrowth"
  //           : "welcomeBusiness";

  //       this.sendEmail(emailKey, user);
  //     }

  //     let sub = null;
  //     if ((isProd && user.id === 1) || shouldNotPay) {
  //       sub = { id: `free_${uuid()}` };
  //     } else {
  //       logger.info(
  //         `Creating subscription ${chosenSub}: ${config.get(
  //           `stripe.subscription.${chosenSub}`
  //         )} for user ${user.id}`
  //       );
  //       sub = await this.stripe.createSubscription({
  //         customer: user.stripeId,
  //         items: [
  //           {
  //             price: config.get(`stripe.subscription.${chosenSub}`),
  //             quantity: 1,
  //           },
  //         ],
  //         default_payment_method: card.id,
  //         cancel_at_period_end: false,
  //         payment_behavior: "error_if_incomplete",
  //         off_session: true,
  //         ...(coupon && { coupon }),
  //         ...(trialOn && { trial_end }),
  //       });
  //     }

  //     if (PLANS.has(chosenSub)) {
  //       try {
  //         user.subscription = chosenSub.split("_")[0]; //growth or business
  //         user.subscriptionId = sub.id;
  //         user.subscriptionStart = new Date();
  //         const tours = await new DbTour().getAllNoLimit(user.id);

  //         const activeTours = tours.filter(
  //           (tour: DbTour) => tour.status === TourStatus.Ready
  //         );

  //         await Promise.all(
  //           tours.map((tour: DbTour) => {
  //             tour.status = TourStatus.Disabled;
  //             return tour.save();
  //           })
  //         );

  //         if (trialOn) {
  //           user.trialActivated = true;
  //         }

  //         if (
  //           chosenSub === Subscription.GrowthMonth ||
  //           chosenSub === Subscription.GrowthYear
  //         ) {
  //           user.hubMinutes = user.hubMinutes += 350;
  //           await this.toggleGreenScreen(user.id, false);
  //         }

  //         if (
  //           chosenSub === Subscription.BusinessMonth ||
  //           chosenSub === Subscription.BusinessYear ||
  //           chosenSub === Subscription.Enterprise
  //         ) {
  //           user.hubMinutes = user.hubMinutes += 5000;
  //           await this.toggleGreenScreen(user.id, true);
  //         }

  //         if (
  //           chosenSub === Subscription.BusinessYear ||
  //           chosenSub === Subscription.Enterprise
  //         ) {
  //           const adminTranslations = await new DbAdminTranslation().getByUser(
  //             user.id
  //           );
  //           if (!adminTranslations) {
  //             const newTranslations = new DbAdminTranslation();
  //             await newTranslations.createFromDefault(user.id);
  //           }
  //         }

  //         if (chosenSub === Subscription.Enterprise) {
  //           //basiaclly unlimited..
  //           user.hubMinutes = 1_000_000;
  //         }

  //         let limit = 5;

  //         if (
  //           chosenSub === Subscription.GrowthMonth ||
  //           chosenSub === Subscription.GrowthYear
  //         )
  //           limit = 50;
  //         if (
  //           chosenSub === Subscription.BusinessMonth ||
  //           chosenSub === Subscription.BusinessYear
  //         )
  //           limit = 999_999;
  //         if (chosenSub === Subscription.Enterprise) limit = 999_999;

  //         await Promise.all(
  //           tours.map((tour: DbTour) => {
  //             if (tours.length <= limit) {
  //               if (tour.status === TourStatus.Ready) return Promise.resolve();
  //               tour.status = TourStatus.Ready;
  //             } else {
  //               if (activeTours.some((t) => t.id === tour.id))
  //                 return Promise.resolve(); //if tour is already active, skip it
  //               if (activeTours.length > limit) {
  //                 tour.status = TourStatus.Disabled; //if there are more active than allowed, disable it
  //               } else {
  //                 //else activate it
  //                 tour.status = TourStatus.Ready;
  //                 activeTours.push(tour);
  //               }
  //             }
  //             return tour.save();
  //           })
  //         );
  //       } catch (error) {
  //         console.error(`Error while setting up user data: ${error}`);
  //         logger.error(`Error while setting up user data: ${error}`);
  //       }

  //       //This order is important, first we save new user, then we handle the "old" user. Otherwise paypal webhooks might fire when not needed causing issues
  //       await user.save();
  //       logger.info(
  //         `User ${user.email} subscribed to ${chosenSub} with id: ${sub.id}`
  //       );
  //       await this.handleOldSubscription(chosenSub, oldUser);
  //     }

  //     if (chosenSub === Subscription.Whitelabel) {
  //       await this.handleWhitelabel(user, sub, req);
  //     }

  //     if (chosenSub === Subscription.MiniWhitelabel) {
  //       const client = await new DbClient().fromId(clientId);
  //       client.whitelabelId = sub.id;
  //       await client.save(client.userId);
  //     }

  //     if (chosenSub === Subscription.MiniFullWhitelabel) {
  //       const client = await new DbClient().fromId(clientId);
  //       client.whitelabelId = sub.id;
  //       client.whitelabelLogo = true;
  //       await client.save(client.userId);
  //     }

  //     if (chosenSub === Subscription.Experience) {
  //       const tour = await new DbTour().fromPk(tourId);
  //       const game = await new DbGame().create();
  //       tour.gameId = game.id;
  //       game.paid = true;
  //       game.subscriptionId = sub.id;
  //       await game.save();
  //       let introScreen = await new DbIntroScreen().getByTour(tour.id);
  //       if (!introScreen) {
  //         introScreen = new DbIntroScreen();
  //         Object.assign(introScreen, { tourId: tour.id, enableLogo: true });
  //         await introScreen.save();
  //       }
  //       await tour.save();
  //     }

  //     if (chosenSub === Subscription.Ticketing) {
  //       const tour = await new DbTour().fromPk(tourId);
  //       tour.subscriptionId = sub.id;
  //       tour.ticket = true;
  //       await tour.save();
  //     }

  //     if (chosenSub === Subscription.Ecommerce) {
  //       const tour = await new DbTour().fromPk(tourId);
  //       tour.mode = "ecommerce";
  //       tour.ecommerceSubscriptionId = sub.id;
  //       await tour.save();
  //     }
  //   } else {
  //     // Send welcome email for free users on first subscription creation
  //     if (sendEmail) {
  //       this.sendEmail("welcomeFree", user);
  //     }

  //     // If isTrialing and chosenSub is Free, it means the user canceled the trial and we need to send the cancel email
  //     if (isTrialing && chosenSub === Subscription.Free) {
  //       this.sendEmail("trialCancel", user);
  //     }

  //     await this.handleOldSubscription(chosenSub, user);
  //     user.subscription = Subscription.Free;
  //     user.subscriptionId = null;
  //     user.subscriptionStart = new Date();
  //     user.hubMinutes = user.hubMinutes += 30;
  //     const tours = await new DbTour().getAllByStatus(
  //       user.id,
  //       TourStatus.Ready
  //     );
  //     await Promise.all(
  //       tours.map((tour: DbTour, index: number) => {
  //         if (index > 4) {
  //           tour.status = TourStatus.Disabled;
  //           return tour.save();
  //         } else {
  //           return Promise.resolve();
  //         }
  //       })
  //     );
  //     await this.toggleGreenScreen(user.id, false);
  //   }

  //   const toursLimit = this.getToursLimit(chosenSub, userId);
  //   user.toursLimit = toursLimit;

  //   await this.lockPlanFeatures(chosenSub, userId, toursLimit);
  //   await user.save();

  //   const isDowngrading =
  //     (oldUser.subscription === "enterprise" && chosenSub !== "enterprise") ||
  //     (oldUser.subscription === "business" && !chosenSub.includes("business"));
  //   if (isDowngrading) {
  //     await new UserController({}).deactivateUserAddons(userId);
  //   }
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("billing/createSubscription")
  // public async createSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   const userId = request.account.id;
  //   const chosenSub = request.body.subscription;
  //   const wlBundle = request.body.wlBundle;
  //   const isProduction = process.env.NODE_ENV === "production";
  //   const coupon = request.body.coupon;
  //   const clientId = request.body.clientId;
  //   const tourId = request.body.tourId;
  //   const sendEmail = request.body.sendEmail;

  //   await this.createSubscriptionLogic({
  //     userId,
  //     chosenSub,
  //     wlBundle,
  //     isProd: isProduction,
  //     coupon,
  //     clientId,
  //     tourId,
  //     sendEmail,
  //     req: request,
  //   });

  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("billing/getSubscription")
  // public async getSubscription(request: RequestType<Request>, res: Response) {
  //   if (request.params.id.includes("free_")) return res.status(200);
  //   const subscription = await this.stripe.getSubscription(request.params.id);
  //   return res.status(200).json({ message: "Success", subscription });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("billing/listSubscriptions")
  // public async listSubscriptions(request: RequestType<Request>, res: Response) {
  //   const user = await new DbUser().fromId(request.account.id);
  //   const subscriptions = await this.stripe.getSubscriptions(user.stripeId);
  //   return res.status(200).json({ message: "Success", subscriptions });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("billing/deleteSubscription")
  // public async deleteSubscription(
  //   request: RequestType<Request>,
  //   res: Response
  // ) {
  //   if (!request.body.subscriptionId.includes("free_")) {
  //     await this.stripe.deleteSubscription(request.params.id);
  //   }
  //   return res.status(200).json({ message: "Success" });
  // }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  // @handleError("billing/coupon")
  // public async getCoupon(request: RequestType<Request>, res: Response) {
  //   const coupon = await this.stripe.getCoupon(request.params.id);
  //   return res.status(200).json({ message: "Success", coupon });
  // }

  // async toggleGreenScreen(userId: number, status: boolean) {
  //   const usersTours = await new DbTour().getAllNoLimit(userId);
  //   for (const tour of usersTours) {
  //     const vsVideos = await new DbVSVideo().getAll(tour.id);
  //     if (vsVideos.length > 0) {
  //       const vsVideosToChange = vsVideos.filter(
  //         (video) => video.greenScreenEnabled === !status
  //       );
  //       for (const element of vsVideosToChange) {
  //         const vsVideoToChange = await new DbVSVideo().get(element.id);
  //         vsVideoToChange.greenScreenEnabled = status;
  //         vsVideoToChange.disabledFromSubscription = !status;
  //         await vsVideoToChange.save();
  //       }
  //     }
  //   }
  // }

  // async disableThreadsForUser(userId: number) {
  //   const usersTours = await new DbTour().getAllNoLimit(userId);
  //   for (const tour of usersTours) {
  //     if (tour.isThreadsEnabled) {
  //       tour.isThreadsEnabled = false;
  //       await tour.save();
  //     }
  //   }
  // }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    this.router.get("/customer", this.getCustomer.bind(this));
    this.router.delete("/card", this.deleteCard.bind(this));
    this.router.get("/card", this.getCard.bind(this));
    this.router.get("/session", this.getSession.bind(this));
    this.router.post("/pay", this.pay.bind(this));
    this.router.post("/payCustom", this.payCustom.bind(this));
    // this.router.post(
    //   "/subscription/create",
    //   this.createSubscription.bind(this)
    // );
    this.router.post(
      "/subscription/update",
      this.updateSubscription.bind(this)
    );
    // this.router.get("/subscription/:id", this.getSubscription.bind(this));
    // this.router.get("/subscription", this.listSubscriptions.bind(this));
    // this.router.delete("/subscription/:id", this.deleteSubscription.bind(this));
    this.router.post("/invoice", this.listInvoices.bind(this));
    this.router.get("/invoice/:id", this.getInvoice.bind(this));
    // this.router.get("/coupon/:id", this.getCoupon.bind(this));
  }
}

export default BillingRouter;

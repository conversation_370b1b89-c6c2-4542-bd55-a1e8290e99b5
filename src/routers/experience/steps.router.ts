import { Request, Response, Router } from 'express';
import ConditionsController from 'src/controllers/experience/conditions';
import { SaveStepRequestDto } from 'src/controllers/experience/dto';
import StepsController from 'src/controllers/experience/steps';
import TriggersController from 'src/controllers/experience/triggers';
import {
  CoreApiResponse,
  handleError,
  mimeByGroup,
  RequestType,
  Role,
  upload,
  validate,
} from 'src/core/common';
import { S3Queue } from 'src/queues';

import authorize, { tourHasAccess } from '../../lib/permission';

class StepsRouter {
  router: Router;

  stepsController: StepsController;

  conditionsController: ConditionsController;

  triggersController: TriggersController;

  constructor() {
    this.router = Router();
    this.init();
    this.stepsController = new StepsController(new S3Queue());
    this.conditionsController = new ConditionsController();
    this.triggersController = new TriggersController();
  }

  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/get')
  public async get(request: RequestType<Request>, res: Response) {
    const steps = await this.stepsController.getAll(request.query.flowId);
    return res.json(CoreApiResponse.success(steps));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @validate(SaveStepRequestDto)
  @handleError('steps/save')
  public async save(request: RequestType<Request>, res: Response) {
    await tourHasAccess(request.account, request.body.tourId);
    const data = await this.stepsController.save(request.body, request.files);
    return res.json(CoreApiResponse.success(data));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/duplicate')
  public async duplicate(req: RequestType<Request>, res: Response) {
    const savedId = await this.stepsController.duplicate(req.body.id);
    const newStep = await this.stepsController.getFullById(savedId);
    return res.json(CoreApiResponse.success(newStep));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/updatePosition')
  public async updatePosition(request: RequestType<Request>, res: Response) {
    await this.stepsController.updatePosition(
      request.body.id,
      request.body.xPos,
      request.body.yPos,
    );
    return res.json(CoreApiResponse.success({ message: 'Success' }));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/delete')
  public async delete(request: RequestType<Request>, res: Response) {
    await this.stepsController.delete(request.body);
    return res.json(CoreApiResponse.success({ message: 'Success' }));
  }

  // EDGES
  // @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/edges/get')
  public async getAll(request: RequestType<Request>, res: Response) {
    const edges = await this.stepsController.getEdges(request.query.flowId);
    return res.json(CoreApiResponse.success(edges));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/edges/save')
  public async saveEdges(request: RequestType<Request>, res: Response) {
    const data = await this.stepsController.saveEdges(
      request.body.flowId,
      request.body.edges,
    );
    return res.json(CoreApiResponse.success(data));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/edges/delete')
  public async deleteEdges(request: RequestType<Request>, res: Response) {
    if (request.body.id) {
      await this.stepsController.deleteById(request.body.id);
    }

    if (request.body.flowId) {
      await this.stepsController.deleteByFlowId(request.body.flowId);
    }

    return res.json(CoreApiResponse.success({ message: 'Success' }));
  }

  // FLOW CONDITIONS
  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/conditions/save')
  public async saveCondition(request: RequestType<Request>, res: Response) {
    const data = await this.conditionsController.save(request.body.condition);
    return res.json(CoreApiResponse.success(data));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/conditions/delete')
  public async deleteCondition(request: RequestType<Request>, res: Response) {
    if (request.body.id) {
      await this.conditionsController.delete(request.body.id);
    }

    return res.json(CoreApiResponse.success({ message: 'Success' }));
  }

  // FLOW TRIGGERS
  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/triggers/save')
  public async saveTrigger(req: RequestType<Request>, res: Response) {
    let condition = null;
    if (req.body.condition) {
      condition = await this.conditionsController.save(req.body.condition);
    }
    const data = await this.triggersController.save({
      ...req.body.trigger,
      conditionId: condition.id,
    });
    const returnValue = {
      ...(data?.dataValues || data[1]?.[0]?.dataValues),
      condition,
    };
    return res.json(CoreApiResponse.success(returnValue));
  }

  @authorize([Role.SuperAdmin, Role.Admin, Role.User])
  @handleError('steps/triggers/delete')
  public async deleteTrigger(request: RequestType<Request>, res: Response) {
    if (request.body.id) {
      await this.triggersController.delete(request.body.id);
    }

    return res.json(CoreApiResponse.success({ message: 'Success' }));
  }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */

  init() {
    this.router.get('/get', this.get.bind(this));
    this.router.post(
      '/save',
      upload([{ name: 'file', maxCount: 1 }], {
        allowTypes: [mimeByGroup.IMAGE, mimeByGroup.VIDEO, mimeByGroup.AUDIO],
        maxFileSize: 25,
      }),
      this.save.bind(this),
    );
    this.router.post('/delete', this.delete.bind(this));
    this.router.post('/duplicate', this.duplicate.bind(this));
    this.router.post('/updatePosition', this.updatePosition.bind(this));
    this.router.get('/edges/get', this.getAll.bind(this));
    this.router.post('/edges/save', this.saveEdges.bind(this));
    this.router.post('/edges/delete', this.deleteEdges.bind(this));
    this.router.post('/conditions/save', this.saveCondition.bind(this));
    this.router.post('/conditions/delete', this.deleteCondition.bind(this));
    this.router.post('/triggers/save', this.saveTrigger.bind(this));
    this.router.post('/triggers/delete', this.deleteTrigger.bind(this));
  }
}

export default StepsRouter;

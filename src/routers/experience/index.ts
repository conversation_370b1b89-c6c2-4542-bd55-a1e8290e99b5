// import { Router } from 'express';
// import FlowRouter from 'src/routers/experience/flow.router';
// import StepsRouter from 'src/routers/experience/steps.router';

// import ExperienceSettingsRouter from './setting.router';

// class ExperienceRouter {
//   router: Router;

//   constructor() {
//     this.router = Router();
//     this.init();
//   }

//   init() {
//     this.router.use('/steps', new StepsRouter().router);
//     this.router.use('/settings', new ExperienceSettingsRouter().router);
//     this.router.use('/flows', new FlowRouter().router);
//   }
// }

// export default ExperienceRouter;

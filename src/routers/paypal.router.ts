import { Request, Response, Router } from "express";
import { logger, LogLevel } from "src/log";

import {
  ClientException,
  Code,
  handleError,
  RequestType,
} from "../core/common";
import { BotQueue, S3Queue } from "../queues";
// import {
//   cancelPaypalSubscription,
//   getCurrencies,
//   getPaypalSubscription,
// } from "./util";

const TOUR_PLAN: string[] = [];
const MONTHLY_PLAN: string[] = [];
const YEARLY_PLAN: string[] = [];
const WHITELABEL_PLAN: string[] = [];
const CLIENT_WHITELABEL_PLAN: string[] = [];
const CLIENT_WHITELABEL_COLOR_PLAN: string[] = [];

const VIDEO_S_PLAN: string[] = [];
const VIDEO_M_PLAN: string[] = [];
const VIDEO_L_PLAN: string[] = [];

const GAME_PLAN: string[] = [];

const ECOMMERCE_PLAN: string[] = [];

const TICKET_PLAN: string[] = [];

const VIDEO_CREDITS = {
  S: 80,
  M: 300,
  L: 2000,
};

// getCurrencies().forEach((current: string) => {
//   TOUR_PLAN.push(process.env[`PAYPAL_TOUR_PLAN_${current}`]);
//   MONTHLY_PLAN.push(process.env[`PAYPAL_MONTHLY_PLAN_${current}`]);
//   YEARLY_PLAN.push(process.env[`PAYPAL_YEARLY_PLAN_${current}`]);
//   WHITELABEL_PLAN.push(process.env[`PAYPAL_j_PLAN_${current}`]);
//   CLIENT_WHITELABEL_PLAN.push(
//     process.env[`PAYPAL_CLIENT_WHITELABEL_PLAN_${current}`]
//   );
//   CLIENT_WHITELABEL_COLOR_PLAN.push(
//     process.env[`PAYPAL_CLIENT_WHITELABEL_COLOR_PLAN_${current}`]
//   );
//   VIDEO_S_PLAN.push(process.env[`PAYPAL_VIDEO_S_PLAN_${current}`]);
//   VIDEO_M_PLAN.push(process.env[`PAYPAL_VIDEO_M_PLAN_${current}`]);
//   VIDEO_L_PLAN.push(process.env[`PAYPAL_VIDEO_L_PLAN_${current}`]);
//   GAME_PLAN.push(process.env[`PAYPAL_GAME_PLAN_${current}`]);
//   TICKET_PLAN.push(process.env[`PAYPAL_TICKET_PLAN_${current}`]);
//   ECOMMERCE_PLAN.push(
//     process.env[`PAYPAL_ECOMMERCE_MONTHLY_PLAN_${current}`],
//     process.env[`PAYPAL_ECOMMERCE_YEARLY_PLAN_${current}`]
//   );
// });

class PaypalRouter {
  router: Router;

  botQueue: BotQueue;

  s3Queue: S3Queue;

  // awsQueue: AWSQueue;

  constructor() {
    this.router = Router();
    this.botQueue = new BotQueue();
    this.s3Queue = new S3Queue();
    // this.awsQueue = new AWSQueue();
    this.init();
  }

  @handleError(
    (request) => `paypal/subscriptionActivated: ID: ${request.body.resource.id}`
  )
  public async subscriptionActivated(
    request: RequestType<Request>,
    res: Response
  ) {
    // if (
    //   MONTHLY_PLAN.includes(request.body.resource.plan_id) ||
    //   YEARLY_PLAN.includes(request.body.resource.plan_id)
    // ) {
    //   const user = await new DbUser().fromSubscription(
    //     request.body.resource.id
    //   );
    //   user.subscriptionStart = new Date();

    //   //UNLOCK TOURS
    //   if (user.subscription == UserSubscription.Pay) {
    //     const tours = await new DbTour().getAllNoLimit(user.id);
    //     await Promise.all(
    //       tours.map(async (tour: any) => {
    //         if (
    //           tour.status == TourStatus.Error ||
    //           tour.status == TourStatus.Idle
    //         )
    //           return;

    //         if (tour.status == TourStatus.Ready && tour.subscriptionId) {
    //           //We need to cancel the subscription
    //           const subId = (" " + tour.subscriptionId).slice(1); //deep copy
    //           tour.subscriptionId = null;
    //           tour.subscriptionStart = null;
    //           await tour.save();
    //           await cancelPaypalSubscription(subId);
    //           logger.info(`Cancelled PAYG ${subId}`);
    //         }

    //         if (tour.status == TourStatus.Pay) {
    //           if (!tour.scannedAt) {
    //             tour.status = TourStatus.Idle;
    //             await tour.save();
    //             this.botQueue
    //               .scanTour({
    //                 id: tour.id,
    //                 io: request.io,
    //               })
    //               .catch((error: any) => {
    //                 logger.error(`Error scanning tour ${tour.id}: ${error}`);
    //               });
    //           } else {
    //             tour.status = TourStatus.Ready;
    //             request.io.emit("bot-job-done", tour);
    //             await tour.save();
    //           }
    //         }

    //         return;
    //       })
    //     );
    //   }

    //   if (MONTHLY_PLAN.includes(request.body.resource.plan_id)) {
    //     user.subscription = UserSubscription.Month;
    //     user.videoCredits += 80;
    //   } else if (YEARLY_PLAN.includes(request.body.resource.plan_id)) {
    //     user.subscription = UserSubscription.Year;
    //     user.videoCredits += 960;
    //   }

    //   await user.save();
    // } else if (VIDEO_S_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.S;
    //   client.subscriptionType = "S";
    //   await client.save(client.userId);
    // } else if (VIDEO_M_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.M;
    //   client.subscriptionType = "M";
    //   await client.save(client.userId);
    // } else if (VIDEO_L_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.L;
    //   client.subscriptionType = "L";
    //   await client.save(client.userId);
    // } else if (GAME_PLAN.includes(request.body.resource.plan_id)) {
    //   const game = await new DbGame().fromSubscription(
    //     request.body.resource.id
    //   );
    //   game.paid = true;
    //   await game.save();
    // } else if (TICKET_PLAN.includes(request.body.resource.plan_id)) {
    //   const ticket = await new DbTicketingGeneral().get({
    //     query: {
    //       subscriptionId: request.body.resource.id,
    //     },
    //   });
    //   ticket.paid = true;
    //   await ticket.save();
    // }
    // logger.info(`paypal/subscriptionActivated: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/subscriptionUpdate")
  public async subscriptionUpdate(
    request: RequestType<Request>,
    res: Response
  ) {
    // if (MONTHLY_PLAN.includes(request.body.resource.plan_id)) {
    //   const user = await new DbUser().fromSubscription(
    //     request.body.resource.id
    //   );
    //   user.subscription = UserSubscription.Month;
    //   await user.save();
    // } else if (YEARLY_PLAN.includes(request.body.resource.plan_id)) {
    //   const user = await new DbUser().fromSubscription(
    //     request.body.resource.id
    //   );
    //   user.subscription = UserSubscription.Year;
    //   await user.save();
    // } else if (VIDEO_S_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.S;
    //   client.subscriptionType = "S";
    //   await client.save(client.userId);
    // } else if (VIDEO_M_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.M;
    //   client.subscriptionType = "M";
    //   await client.save(client.userId);
    // } else if (VIDEO_L_PLAN.includes(request.body.resource.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.L;
    //   client.subscriptionType = "L";
    //   await client.save(client.userId);
    // }
    // logger.info(`paypal/subscriptionUpdate: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/subscriptionFailed")
  public async subscriptionFailed(
    request: RequestType<Request>,
    res: Response
  ) {
    // //TODO: Revert user back to trial and set all his tours on pay:true
    // //TODO: Set subscriptionStart to now.
    // logger.error("Subscription failed");
    // logger.info(JSON.stringify(request.body.resource));
    // //Send an email to notify on failed payment, use counter to count how many times it was failed
    // logger.info(`paypal/subscriptionFailed: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/subscriptionCancelled")
  public async subscriptionCancelled(
    request: RequestType<Request>,
    res: Response
  ) {
    // if (request.body.event_type === "BILLING.SUBSCRIPTION.SUSPENDED") {
    //   //We cancel suspended ones
    //   await cancelPaypalSubscription(request.body.resource.id);
    //   return res.status(200).json({ message: "Success" });
    // }

    // logger.info(
    //   `paypal/subscriptionCancelled: Cancelling ${request.body.resource.id} PLAN: ${request.body.resource.plan_id}`
    // );

    // if (TOUR_PLAN.includes(request.body.resource.plan_id)) {
    //   const tour = await new DbTour().fromSubscription(
    //     request.body.resource.id
    //   );
    //   if (tour) {
    //     tour.status = TourStatus.Pay;
    //     tour.subscriptionId = null;
    //     await tour.save();
    //     request.io.emit("tour-subscription-cancelled", tour.id);
    //   }
    // } else if (WHITELABEL_PLAN.includes(request.body.resource.plan_id)) {
    //   const user = await new DbUser()
    //     .fromSubscription(request.body.resource.id)
    //     .catch(() => {
    //       return false;
    //     });
    //   if (user) {
    //     user.whitelabelId = null;
    //     user.domainUrl = null;
    //     user.adminUrl = null;
    //     user.whitelabelEmail = null;
    //     user.primaryColor = null;
    //     user.secondaryColor = null;
    //     await user.save();
    //   }
    // } else if (
    //   CLIENT_WHITELABEL_PLAN.includes(request.body.resource.plan_id) ||
    //   CLIENT_WHITELABEL_COLOR_PLAN.includes(request.body.resource.plan_id)
    // ) {
    //   const client = await new DbClient()
    //     .fromWhitelabelId(request.body.resource.id)
    //     .catch(() => {
    //       return false;
    //     });
    //   if (client) {
    //     client.whitelabelId = null;
    //     client.whitelabelLogo = false;
    //     client.primaryColor = null;
    //     client.secondaryColor = null;
    //     await client.save(client.userId);
    //   }
    // } else if (
    //   VIDEO_S_PLAN.includes(request.body.resource.plan_id) ||
    //   VIDEO_M_PLAN.includes(request.body.resource.plan_id) ||
    //   VIDEO_L_PLAN.includes(request.body.resource.plan_id)
    // ) {
    //   const client = await new DbClient().fromSubscription(
    //     request.body.resource.id
    //   );
    //   client.monthlyVideoCredits = 0;
    //   client.subscriptionId = null;
    //   client.subscriptionType = null;
    //   await client.save(client.userId);
    // } else if (GAME_PLAN.includes(request.body.resource.plan_id)) {
    //   const game = await new DbGame().fromSubscription(
    //     request.body.resource.id
    //   );
    //   game.paid = false;
    //   game.subscriptionId = null;
    //   await game.save();
    // } else if (ECOMMERCE_PLAN.includes(request.body.resource.plan_id)) {
    //   const tour = await new DbTour().fromEcommerceSubscription(
    //     request.body.resource.id
    //   );
    //   tour.ecommerceSubscriptionId = null;
    //   if (tour.vsPaymentId && tour.vsPaymentId === "ecommerce")
    //     tour.vsPaymentId = null;
    //   await tour.save();
    // } else if (TICKET_PLAN.includes(request.body.resource.plan_id)) {
    //   const ticket = await new DbTicketingGeneral().get({
    //     query: {
    //       subscriptionId: request.body.resource.id,
    //     },
    //   });
    //   ticket.paid = true;
    //   await ticket.save();
    // } else {
    //   const user = await new DbUser().fromSubscription(
    //     request.body.resource.id
    //   );
    //   logger.info(`User ${user.email} cancelled paypal sub. Blocking tours`);
    //   user.subscription = UserSubscription.Pay;
    //   user.subscriptionId = null;
    //   user.subscriptionStart = new Date();
    //   await user.save();
    //   const tours = await new DbTour().getAllByStatus(
    //     user.id,
    //     TourStatus.Ready
    //   );
    //   await Promise.all(
    //     tours.map((tour: any) => {
    //       tour.status = TourStatus.Pay;
    //       return tour.save();
    //     })
    //   );
    //   request.io.emit("user-subscription-cancelled", user.id);
    // }
    // logger.info(`paypal/subscriptionCancelled: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/payment/create")
  public async paymentCreate(request: RequestType<Request>, res: Response) {
    // if (!request.body.resource && !request.body.resource.id) {
    //   throw ClientException.new({
    //     code: Code.BAD_REQUEST_ERROR,
    //     overrideMessage: "Missing params",
    //   });
    // }
    // const purchase = request.body.resource.purchase_units;
    // const payment = await new DbPayment();
    // payment.orderId = request.body.resource.id;
    // payment.type = "paypal";
    // if (purchase && purchase.length > 0 && purchase[0].amount) {
    //   payment.price = Number.parseFloat(purchase[0].amount.value);
    //   payment.currency = purchase[0].amount.currency_code;
    // }
    // await payment.save(request.body.userId);
    // logger.info(`paypal/payment/create: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/payment/created")
  public async paymentCreated(request: RequestType<Request>, res: Response) {
    // if (!request.body.resource && !request.body.resource.id) {
    //   throw ClientException.new({
    //     code: Code.BAD_REQUEST_ERROR,
    //     overrideMessage: "Missing params",
    //   });
    // }
    // const orderId = request.body.resource.id;
    // const purchase = request.body.resource.purchase_units;
    // const payment = await new DbPayment().fromOrderId(orderId);
    // if (!payment) {
    //   throw ClientException.new({
    //     code: Code.NOT_FOUND,
    //     overrideMessage: "No payment found",
    //   });
    // }

    // if (purchase && purchase.length > 0 && purchase[0].amount) {
    //   payment.price = Number.parseFloat(purchase[0].amount.value);
    //   payment.currency = purchase[0].amount.currency_code;
    // }

    // payment.type = "paypal";
    // await payment.save(payment.userId);
    // logger.info(`paypal/payment/created: ${request.body.resource.id}`);
    return res.status(200).json({ message: "Success" });
  }

  @handleError("paypal/payment/sale")
  public async paymentSale(request: RequestType<Request>, res: Response) {
    // if (!request.body.resource && !request.body.resource.billing_agreement_id) {
    //   throw ClientException.new({
    //     code: Code.BAD_REQUEST_ERROR,
    //     overrideMessage: "Missing params",
    //   });
    // }

    // const payment = await new DbPayment();
    // payment.type = "paypal";
    // payment.orderId = request.body.resource.id;
    // payment.subscriptionId = request.body.resource.billing_agreement_id;
    // payment.price = Number.parseFloat(request.body.resource.amount.total);
    // payment.currency = request.body.resource.amount.currency;
    // const subscription = await getPaypalSubscription(payment.subscriptionId);
    // let userId: number;

    // if (TOUR_PLAN.includes(subscription.data.plan_id)) {
    //   const tour = await new DbTour().fromSubscription(payment.subscriptionId);
    //   if (!tour) {
    //     throw ClientException.new({
    //       code: Code.BAD_REQUEST_ERROR,
    //       overrideMessage: `No tour for subscription ${payment.subscriptionId} found`,
    //       metadata: { logLevel: LogLevel.INFO },
    //     });
    //   }
    //   userId = tour.userId;
    //   payment.tourId = tour.id;
    // } else if (
    //   MONTHLY_PLAN.includes(subscription.data.plan_id) ||
    //   YEARLY_PLAN.includes(subscription.data.plan_id) ||
    //   WHITELABEL_PLAN.includes(subscription.data.plan_id)
    // ) {
    //   const user = await new DbUser().fromSubscription(payment.subscriptionId);
    //   userId = user.id;
    // } else if (
    //   CLIENT_WHITELABEL_PLAN.includes(subscription.data.plan_id) ||
    //   CLIENT_WHITELABEL_COLOR_PLAN.includes(subscription.data.plan_id)
    // ) {
    //   const client = await new DbClient().fromWhitelabelId(
    //     payment.subscriptionId
    //   );
    //   userId = client.userId;
    //   payment.clientId = client.id;
    // } else if (VIDEO_S_PLAN.includes(subscription.data.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     payment.subscriptionId
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.S;
    //   await client.save(client.userId);
    //   userId = client.userId;
    //   payment.clientId = client.id;
    // } else if (VIDEO_M_PLAN.includes(subscription.data.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     payment.subscriptionId
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.M;
    //   await client.save(client.userId);
    //   userId = client.userId;
    //   payment.clientId = client.id;
    // } else if (VIDEO_L_PLAN.includes(subscription.data.plan_id)) {
    //   const client = await new DbClient().fromSubscription(
    //     payment.subscriptionId
    //   );
    //   client.monthlyVideoCredits = VIDEO_CREDITS.L;
    //   await client.save(client.userId);
    //   userId = client.userId;
    //   payment.clientId = client.id;
    // } else if (GAME_PLAN.includes(subscription.data.plan_id)) {
    //   const game = await new DbGame().fromSubscription(payment.subscriptionId);
    //   payment.gameId = game.id;
    // } else if (ECOMMERCE_PLAN.includes(subscription.data.plan_id)) {
    //   const tour = await new DbTour().fromSubscription(payment.subscriptionId);
    //   payment.ecommerceId = tour.id;
    // } else if (TICKET_PLAN.includes(request.body.resource.plan_id)) {
    //   const ticket = await new DbTicketingGeneral().get({
    //     query: {
    //       subscriptionId: request.body.resource.id,
    //     },
    //   });
    //   payment.ticketId = ticket.id;
    // }

    // await payment.save(userId);
    // logger.info(
    //   `paypal/payment/sale: ${request.body.resource.billing_agreement_id} ${request.body.resource.amount.total} ${request.body.resource.amount.currency}`
    // );
    return res.status(200).json({ message: "Success" });
  }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    this.router.post(
      "/subscription/activated",
      this.subscriptionActivated.bind(this)
    );
    this.router.post(
      "/subscription/update",
      this.subscriptionUpdate.bind(this)
    );

    this.router.post(
      "/subscription/failed",
      this.subscriptionFailed.bind(this)
    );
    this.router.post(
      "/subscription/cancelled",
      this.subscriptionCancelled.bind(this)
    );
    this.router.post("/payment/created", this.paymentCreated.bind(this));
    this.router.post("/payment/create", this.paymentCreate.bind(this));
    this.router.post("/payment/sale", this.paymentSale.bind(this));
  }
}

export default PaypalRouter;

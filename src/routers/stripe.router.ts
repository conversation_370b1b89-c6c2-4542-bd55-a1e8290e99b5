import { Request, Response, Router } from "express";
import config from "src/config";
import StripePayment from "src/core/service/stripe";
import { logger } from "src/log";
import { EmailQueue } from "src/queues";
import Stripe from "stripe";

import { Code, handleError, RequestType } from "../core/common";

class StripeRouter {
  router: Router;

  stripe: StripePayment;

  emailQueue: EmailQueue;

  constructor() {
    this.router = Router();
    this.stripe = new StripePayment({ apiKey: config.get("stripe.apiKey") });
    this.emailQueue = new EmailQueue();
    this.init();
  }

  // @handleError("stripe/subscription/deleted")
  // public async deleted(request: RequestType<Request>, res: Response) {
  //   const { object } = request.body.data;

  //   if (object.object === "subscription" && object.status === "canceled") {
  //     logger.info("Deleting stripe subscription " + object.id);
  //     const subscription = this.stripe.getPlan(object.plan?.id);
  //     const customer: Stripe.Customer = (await this.stripe.getCustomer(
  //       object.customer
  //     )) as Stripe.Customer;
  //     const user = await new DbUser().fromEmail(customer.email);
  //     if (
  //       (subscription === "growth" ||
  //         subscription === "business" ||
  //         subscription === "enterprise") &&
  //       user.subscriptionId === object.id
  //     ) {
  //       //user.subscription === subscription check is there because want to disable only users who did not upgrade/downgrade.
  //       logger.info(
  //         `User ${user.email} canceled subscription ${subscription} with id: ${object.id}, deactivating tours`
  //       );
  //       user.subscription = "free";
  //       user.subscriptionId = null;
  //       await user.save();
  //       const tours = await new DbTour().getAllByStatus(
  //         user.id,
  //         TourStatus.Ready
  //       );
  //       await Promise.all(
  //         tours.map((tour: DbTour, index: number) => {
  //           if (index > 4) {
  //             tour.status = TourStatus.Disabled;
  //             return tour.save();
  //           } else {
  //             return Promise.resolve();
  //           }
  //         })
  //       );

  //       // disable green screens
  //       const usersTours = await new DbTour().getAllNoLimit(user.id);
  //       for (const tour of usersTours) {
  //         const vsVideos = await new DbVSVideo().getAll(tour.id);
  //         if (vsVideos.length > 0) {
  //           const vsVideosToChange = vsVideos.filter(
  //             (video) => video.greenScreenEnabled === true
  //           );
  //           for (const element of vsVideosToChange) {
  //             const vsVideoToChange = await new DbVSVideo().get(element.id);
  //             vsVideoToChange.greenScreenEnabled = false;
  //             vsVideoToChange.disabledFromSubscription = true;
  //             await vsVideoToChange.save();
  //           }
  //         }
  //       }

  //       if (subscription === "growth") {
  //         const coupon = await this.stripe.createDiscount(
  //           "10% off",
  //           10,
  //           process.env.GROWTH_PROD_ID
  //         );
  //         this.emailQueue
  //           .sendEmail("discountGrowth", process.env.EMAIL_FROM, user.email, {
  //             name: user.name,
  //             coupon,
  //           })
  //           .catch((error: any) => {
  //             logger.error(
  //               `Error sending discountGrowth email to ${user.email}: ${error}`
  //             );
  //           });
  //       }

  //       if (subscription === "business") {
  //         const coupon = await this.stripe.createDiscount(
  //           "10% off",
  //           10,
  //           process.env.BUSINESS_PROD_ID
  //         );
  //         this.emailQueue
  //           .sendEmail("discountBusiness", process.env.EMAIL_FROM, user.email, {
  //             name: user.name,
  //             coupon,
  //           })
  //           .catch((error: any) => {
  //             logger.error(
  //               `Error sending discountBusiness email to ${user.email}: ${error}`
  //             );
  //           });
  //       }

  //       //no discount for enterprise!
  //     }
  //     if (subscription === "whitelabel") {
  //       user.whitelabelId = null;
  //       user.domainUrl = null;
  //       await user.save();
  //     }

  //     if (subscription === "mini_whitelabel") {
  //       const client = await new DbClient().fromWhitelabelId(object.id);
  //       client.whitelabelId = null;
  //       await client.save(client.userId);
  //     }

  //     if (subscription === "ar") {
  //       const tour = await new DbTour().fromSubscription(object.id);
  //       tour.ar = false;
  //       await tour.save();
  //     }

  //     if (subscription === "experience") {
  //       const game = await new DbGame().fromSubscription(object.id);
  //       await game.destroy();
  //     }

  //     if (subscription === "ticketing") {
  //       const tour = await new DbTour().fromSubscription(object.id);
  //       tour.ticket = false;
  //       await tour.save();
  //     }

  //     if (subscription === "ecommerce") {
  //       const tour = await new DbTour().fromEcommerceSubscription(object.id);
  //       tour.mode = TourMode.Regular;
  //       tour.ecommerceSubscriptionId = null;
  //       await tour.save();
  //     }

  //     if (subscription === "mini_full_whitelabel") {
  //       const client = await new DbClient().fromWhitelabelId(object.id);
  //       client.whitelabelId = null;
  //       client.whitelabelLogo = false;
  //       await client.save(client.userId);
  //     }
  //   }
  //   return res.sendStatus(Code.SUCCESS.code);
  // }

  // @handleError("stripe/subscription/updated")
  // public async updated(request: RequestType<Request>, res: Response) {
  //   const { object, previous_attributes: previousAttributes } =
  //     request.body.data;

  //   if (object.object === "subscription" && object.status === "active") {
  //     logger.info("Updating stripe subscription " + object.id);
  //     const subscription = this.stripe.getPlan(object.plan?.id);
  //     const customer: Stripe.Customer = (await this.stripe.getCustomer(
  //       object.customer
  //     )) as Stripe.Customer;
  //     const card = await this.stripe.getCard(customer.id);
  //     const user = await new DbUser().fromEmail(customer.email);
  //     const tours = await new DbTour().getAllNoLimit(user.id);
  //     const activeTours = tours.filter((t) => t.status === TourStatus.Ready);
  //     const isGrowth = subscription === "growth";
  //     const isBusiness = subscription === "business";
  //     const isEnterprise = subscription === "enterprise";

  //     if (isGrowth || isBusiness || isEnterprise) {
  //       user.subscription = subscription;
  //       user.subscriptionId = object.id;
  //       await user.save();

  //       let limit = 5;
  //       if (isGrowth) limit = 50;
  //       if (isBusiness) limit = 999_999;
  //       if (isEnterprise) limit = 999_999; //just big number

  //       await Promise.all(
  //         tours.map((tour: DbTour) => {
  //           if (tours.length <= limit) {
  //             if (tour.status === TourStatus.Ready) return Promise.resolve();
  //             tour.status = TourStatus.Ready;
  //           } else {
  //             if (activeTours.some((t) => t.id === tour.id))
  //               return Promise.resolve(); //if tour is already active, skip it
  //             if (activeTours.length > limit) {
  //               tour.status = TourStatus.Disabled; //if there are more active than allowed, disable it
  //             } else {
  //               //else activate it
  //               tour.status = TourStatus.Ready;
  //               activeTours.push(tour);
  //             }
  //           }
  //           return tour.save();
  //         })
  //       );

  //       // If there's no card, it means the user deleted his card and the subscription will be canceled after a couple of retries/days
  //       if (
  //         previousAttributes?.status === "trialing" &&
  //         card &&
  //         (isGrowth || isBusiness)
  //       ) {
  //         const emailType = isGrowth ? "trialEndGrowth" : "trialEndBusiness";

  //         this.emailQueue
  //           .sendEmail(emailType, process.env.EMAIL_FROM, user.email, {
  //             name: user.name,
  //           })
  //           .catch((error: any) => {
  //             logger.error(error.message);
  //           });
  //       }
  //     }

  //     if (subscription === "whitelabel") {
  //       user.whitelabelId = object.id;
  //       await user.save();
  //     }

  //     //Can't do client whitelabel in webhook because user has to pass somehow clientId...
  //     //same for game, or ecommerce
  //     // if(subscription === "mini_whitelabel") {
  //     //   const client = await new DbClient().fromWhitelabelId(object.id);
  //     //   client.whitelabelId = sub.id;
  //     //   await client.save();
  //     // }

  //     // if(subscription === "mini_full_whitelabel") {
  //     //   const client = await new DbClient().fromWhitelabelId(object.id);
  //     //   client.whitelabelId = sub.id;
  //     //   client.whitelabelLogo = true;
  //     //   await client.save();
  //     // }
  //   }
  //   return res.sendStatus(Code.SUCCESS.code);
  // }

  /**
   * Take each handler, and attach to one of the Express.Router's
   * endpoints.
   */
  init() {
    // this.router.post("/subscription/updated", this.updated.bind(this));
    // this.router.post("/subscription/deleted", this.deleted.bind(this));
  }
}

export default StripeRouter;

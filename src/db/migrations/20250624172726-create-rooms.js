// migrations/XXXXXX-create-rooms.js
"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.createTable(
        "rooms",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          roomNumber: {
            type: Sequelize.STRING(10),
            allowNull: false,
          },
          building: {
            type: Sequelize.STRING,
            allowNull: false,
            defaultValue: "Main",
          },
          floor: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          categoryId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: "roomCategories",
              key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "CASCADE",
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
          },
        },
        { transaction }
      );

      await queryInterface.addIndex("rooms", ["categoryId"], {
        transaction,
      });
      await queryInterface.addIndex("rooms", ["roomNumber"], {
        unique: true,
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.dropTable("rooms", { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};

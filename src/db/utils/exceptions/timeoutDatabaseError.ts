import { BaseError } from './baseError';

export class TimeoutDatabaseError extends BaseError {
  public status: number;

  public isInternalError: boolean;

  public message: string;

  public dbMessage: string;

  constructor(dbMessage: string) {
    super();
    this.message = 'Timeout request';
    this.status = 408;
    this.isInternalError = true;
    this.dbMessage = dbMessage;
    Error.captureStackTrace(this, this.constructor);
  }
}

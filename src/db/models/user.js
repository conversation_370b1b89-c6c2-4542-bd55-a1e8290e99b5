"use strict";
module.exports = (sequelize, DataTypes) => {
  var user = sequelize.define(
    "user",
    {
      name: { type: DataTypes.STRING, allowNull: false },
      email: { type: DataTypes.STRING, allowNull: false },
      country: DataTypes.STRING,
      password: DataTypes.STRING,
      confirmed: { type: DataTypes.BOOLEAN, defaultValue: false },
      subscriptionStart: { type: DataTypes.DATE, allowNull: false },
      subscriptionCycle: DataTypes.ENUM("month", "year"),
      type: DataTypes.ENUM("admin", "superAdmin", "manager", "employee"),
      company: DataTypes.STRING,
      phone: DataTypes.STRING,
      twoFaEnabled: { type: DataTypes.BOOLEAN, defaultValue: false },
    },
    {}
  );

  user.associate = function (models) {
    // models.user.belongsTo(models.user, {
    //   foreignKeyConstraint: true,
    //   // foreignKey: "adminId",
    //   onDelete: "SET NULL",
    //   onUpdate: "CASCADE",
    //   // as: "admin",
    // });
    // models.user.belongsToMany(models.permission, {
    //   through: models.userPermission,
    //   as: "permissions",
    //   foreignKey: "userId",
    //   otherKey: "permissionId",
    // });
  };
  return user;
};

// models/property.js
"use strict";
module.exports = (sequelize, DataTypes) => {
  const property = sequelize.define(
    "property",
    {
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {}
  );

  property.associate = function (models) {
    property.hasMany(models.roomCategory, {
      foreignKey: "propertyId",
      as: "roomCategories",
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    });
  };

  return property;
};

import handleDBError, { BadDataError } from "../utils/handleDBError";

export interface IModel<T> {
  getById: (id: number) => Promise<T>;
  get: (data: any) => Promise<T | null>;
  getAll: (data: any) => Promise<T[]>;
  save: (fieldName?: string, fieldValue?: number) => Promise<T>;
  destroy: (id: number) => void;
}

interface IGetAll {
  where?: Record<string, string>;
  order?: string[][];
  include?: (Record<string, string> | { all: boolean; nest?: boolean })[];
  raw?: boolean;
  nest?: boolean;
  attributes?: string[];
}

interface IGetAllParams {
  where?: any;
  sort?: string[][];
  includeAll?: boolean;
  raw?: boolean;
  nest?: boolean;
  disableCatchError?: boolean;
  attributes?: string[];
  include?: Record<string, string>[];
}

export const ModelName = Symbol("modelName");

export class Model<T, U> implements IModel<T> {
  protected dbInstance: any;

  protected model: any;

  protected id?: number;

  constructor(dbInstance: any, model: any) {
    console.log("dbInstance!!!!", dbInstance);
    this.dbInstance = dbInstance;
    this.model = model;
    Object.assign(this, model);
    this[ModelName] = model.constructor.name;
  }

  @handleDBError()
  async getById(id: number, options: any = null): Promise<T> {
    const record = await this.dbInstance.findByPk(id, options);

    if (record == undefined) {
      throw new BadDataError(
        404,
        `Failed to fetch ${this[ModelName]} with id ${id} from the database`
      );
    }

    this.id = record.id;
    return (Object as any).assign(this, record.toJSON() as U) as T;
  }

  @handleDBError()
  async get(data: IGetAllParams, options: object = {}): Promise<T | null> {
    const params: IGetAll = {};
    if (data?.where) {
      params.where = data.where;
    }

    if (data?.sort) {
      params.order = data.sort;
    }

    if (data?.includeAll) {
      params.include = [
        {
          all: true,
        },
      ];
    }

    if (data?.include) {
      params.include = data.include;
    }

    if (data?.attributes) {
      params.attributes = [...data.attributes];
    }

    const record = await this.dbInstance.findOne(params, options);

    if (record == undefined) {
      if (data?.disableCatchError) return null;

      throw new BadDataError(
        404,
        `Failed to fetch ${this[ModelName]} from the database`
      );
    }

    this.id = record.id;
    return (Object as any).assign(this, record.toJSON() as U) as T;
  }

  async count(data: IGetAllParams): Promise<T[]> {
    const params: IGetAll = {};

    if (data?.where) {
      params.where = data.where;
    }

    return await this.dbInstance.count(params);
  }

  @handleDBError()
  async getAll(data: IGetAllParams = {}): Promise<T[]> {
    console.log("hasnuma model get all!!!!!!!");
    const params: IGetAll = {};

    if (data?.sort) {
      params.order = data?.sort || [["id", "ASC"]];
    }

    if (data?.where) {
      params.where = data.where;
    }

    if (data?.includeAll) {
      params.include = [{ all: true, nest: true }];
    }

    if (data?.include) {
      params.include = data.include;
    }

    if (data?.nest) {
      params.nest = true;
    }

    if (data?.raw) {
      params.raw = true;
    }

    if (data?.attributes) {
      params.attributes = data.attributes;
    }

    return this.dbInstance.findAll(params);
  }

  @handleDBError()
  async save(fieldName?: string, fieldValue?: number, params?: any) {
    const recordToUpsert = (Object as any).assign({}, this as unknown as U);
    if (fieldName && fieldValue) {
      recordToUpsert[fieldName] = fieldValue;
    }
    recordToUpsert.updatedAt = null;
    const options: any = {
      returning: true,
    };
    if (params?.transaction) {
      options.transaction = params.transaction;
    }

    if (!recordToUpsert.id) delete recordToUpsert.id;
    console.log("this.dbInstance!!!", this.dbInstance);
    const [record] = await this.dbInstance.upsert(recordToUpsert, options);
    this.id = record.id;
    return (Object as any).assign({}, record.toJSON() as U) as T;
  }

  @handleDBError()
  async destroy() {
    if (this.id) {
      await this.dbInstance.destroy({
        where: {
          id: this.id,
        },
      });
      this.id = undefined;
    } else {
      throw new BadDataError(
        400,
        `Cannot delete ${this[ModelName]} with no id`
      );
    }
  }

  mapFrom<S, K>(item): S {
    return (Object as any).assign({}, item.toJSON() as K) as S;
  }

  mapFromMulti<S, K>(items): Promise<S[]> {
    return Promise.all(items.map((item) => this.mapFrom<S, K>(item)));
  }
}

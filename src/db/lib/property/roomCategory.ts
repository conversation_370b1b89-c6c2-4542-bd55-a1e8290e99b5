const db = require("../../models");
import { Model } from "../model";
import handleDBError, { BadDataError } from "../../utils/handleDBError";

export class RoomCategory {
  id?: number;

  enabled: boolean;

  name: string;

  shortName: string;

  photoCount: number;

  mainPlaces: number;

  additionalPlaces: number;

  propertyId: number;

  constructor() {
    this.name = "";
    this.enabled = true;
    this.mainPlaces = 2;
  }
}

export class DbRoomCategory extends Model<DbRoomCategory, RoomCategory> {
  constructor() {
    console.log("db!!!", db);
    super(db.roomCategory, new RoomCategory());
  }

  @handleDBError()
  async getRoomCategoriesWithRooms(propertyId) {
    console.log("propertyId!!!", propertyId);
    console.log("this!!!!!!!!!", this.dbInstance);
    try {
      const categories = this.dbInstance.getAll({
        where: propertyId,
        include: [
          {
            model: db.room,
          },
        ],
      });
      console.log("categories in lib!!!!!", categories);
      return categories;
    } catch (error) {
      console.log("error!!!!!", error);
    }
  }
}

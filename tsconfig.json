{"compilerOptions": {"target": "es2020", "emitDecoratorMetadata": true, "experimentalDecorators": true, "module": "commonjs", "outDir": "dist", "sourceMap": true, "rootDir": "src", "baseUrl": "./", "strict": false, "skipLibCheck": true, "removeComments": true, "resolveJsonModule": true, "paths": {"src/*": ["src/*"]}, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "static", "dist", "lambdas"], "lib": ["es6", "es8", "ES2020.Promise"]}